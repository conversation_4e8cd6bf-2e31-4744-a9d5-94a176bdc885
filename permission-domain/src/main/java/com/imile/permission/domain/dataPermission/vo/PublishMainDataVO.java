package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/8
 */
@Data
public class PublishMainDataVO {

    private Long id;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    private String dataUrl;

    /**
     * 请求类型
     */
    private String requestType;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    private String typeNameEn;

    /**
     * 建议引用数据类型名称
     */
    private String suggestRefTypeCode;

    /**
     * 所属系统
     */
    private String singleSystem;


    /**
     * 函数数据
     */
    private List<DynamicDataCodeVO> dynamicDataCodeList;

    /**
     * 数据维度
     */
    private List<RuleDataVO> ruleDataList;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

}
