package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class UpdateCustomDynamicDataConfigParam {

    private Long customDynamicId;

    /**
     * 数据维度
     * 0：单维度
     * 1：多维度
     */
    private Integer dimension;

    /**
     * 单维度数据配置
     */
    private SingleDimensionUpdateParam singleDimensionParam;

    /**
     * 多维度数据配置
     */
    private MultiDimensionUpdateParam multiDimensionParam;


}
