package com.imile.permission.data.ds;

import com.imile.idwork.IdWorkerUtil;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

public class MexTest {




    @Test
    public void test() {
        String s = "('%s', 'g', 'U:%s', 'R:1782609277233500162'),";
        list.forEach(x -> System.out.println(String.format(s, IdWorkerUtil.getId(), x)));
    }

    List<String> list = Arrays.asList(
            "D21011814201",
            "D21011780001",
            "D21011779501",
            "D21011776101",
            "D21011746301",
            "D21011746201",
            "D21011746101",
            "D21011746001",
            "D21011745901",
            "D21011745801",
            "D21011745701",
            "D21011744401",
            "D21011744001",
            "D21011743901",
            "D21011743701",
            "D21011743601",
            "D21011743501",
            "D21011743401",
            "D21011742001",
            "D21011739101",
            "D21011732401",
            "D21011731601",
            "D21011715801",
            "D21011715701",
            "D21011708401",
            "D21011707601",
            "D21011688501",
            "D21011660401",
            "D21011652201",
            "D21011652001",
            "D21011651801",
            "D21011651701",
            "D21011639301",
            "D21011639201",
            "D21011638601",
            "D21011638401",
            "D21011638101",
            "D21011637501",
            "D21011637201",
            "D21011637101",
            "D21011636601",
            "D21011636301",
            "D21011635201",
            "D21011635101",
            "D21011635001",
            "D21011546601",
            "D21011843901",
            "D21012018401",
            "D21012016201",
            "D21012015001",
            "D21012010601",
            "D21012004601",
            "D21012001901",
            "D21011956101",
            "D21011943301",
            "D21011928401",
            "D21011913501",
            "D21011877801",
            "D21011877101",
            "D21011872301",
            "D21011870501",
            "D21011869501",
            "D21011855901",
            "D21011420801",
            "D21011420701",
            "D21011371501",
            "D21011104801",
            "D21011035301",
            "D21011030001",
            "D21011029401",
            "D21010770701",
            "D21010739401",
            "D21010739101",
            "D21010715701",
            "D21010627201",
            "D21010564201",
            "D21010529301",
            "D21010501901",
            "D21010477301",
            "D21010474701",
            "D21010472501",
            "D21010446101",
            "D21010359601",
            "D21010351801",
            "D21010351601",
            "D21010299501",
            "D21010287401",
            "D21010246801",
            "D21010245301",
            "D21010241401",
            "D21010191901",
            "D21010191501",
            "D21010164701",
            "D21010091801",
            "D21010053601",
            "D21010051101",
            "D21010050801",
            "D21010034501",
            "D21010026901",
            "D21010021201",
            "D2109976801",
            "D2109953601",
            "D2109953201",
            "D2109930501",
            "D2109870501",
            "D2109858401",
            "D2109842601",
            "D2109842501",
            "D2109842101",
            "D2109841601",
            "D2109841501",
            "D2109839001",
            "D2109821601",
            "D2109806401",
            "D2109765501",
            "D2109722101",
            "D2109707601",
            "D2109685401",
            "D2109680801",
            "D2109680501",
            "D2109662501",
            "D2109658701",
            "D2109620801",
            "D2109620501",
            "D2109600201",
            "D2109599801",
            "D2109569501",
            "D2109562001",
            "D2109561301",
            "D2109558001",
            "D2109548001",
            "D2109545201",
            "D2109541901",
            "D2109517501",
            "D2109508801",
            "D2109500101",
            "D2109499801",
            "D2109499501",
            "D2109499401",
            "D2109499301",
            "D2109499201",
            "D2109499101",
            "D2109494701",
            "D2109494601",
            "D2109483401",
            "D2109482901",
            "D2109482401",
            "D2109466401",
            "D2109463601",
            "D2109463501",
            "D2109461501",
            "D2109456001",
            "D2109455801",
            "D2109441501",
            "D2109412301",
            "D2109411901",
            "D2109411701",
            "D2109409601",
            "D2109408901",
            "D2109408701",
            "D2109402801",
            "D2109377601",
            "D2109325701",
            "D2109325501",
            "D2109310401",
            "D2109237701",
            "D2109231501",
            "D2109207501",
            "D2109207001",
            "D2109189901",
            "D2109189201",
            "D2109189101",
            "D2109189001",
            "D2109186001",
            "D2109185201",
            "D2109185001",
            "D2109184801",
            "D2109184201",
            "D2109184001",
            "D2109183501",
            "D2109163901",
            "D2109123501",
            "D2109122301",
            "D2109084701",
            "D2109005401",
            "D2109004101",
            "D2108947001",
            "D2108691001",
            "D2108620201",
            "D2108600201",
            "D2108598701",
            "D2108574001",
            "D2108573801",
            "D2108482001",
            "D2108433801",
            "D2108412001",
            "D2108359601",
            "D2108357801",
            "D2108357301",
            "D2108357001",
            "D2108356401",
            "D2108327401",
            "D2108288701",
            "D2108285501",
            "D2108263701",
            "D2108261701",
            "D2108261401",
            "D2108257801",
            "D2108240701",
            "D2108176001",
            "D2108175701",
            "D2108175501",
            "D2108171701",
            "D2107919201",
            "D2107869601",
            "D2107867801",
            "D2107850701",
            "D2107846201",
            "D2107811001",
            "D2107754701",
            "D2107681001",
            "D2107680701",
            "D2107680401",
            "D2107676001",
            "D2107615501",
            "D2107611901",
            "D2107611401",
            "D2107574301",
            "D2107569601",
            "D2107564501",
            "D2107563301",
            "D2107505701",
            "D2107493301",
            "D2107489601",
            "D2107454401",
            "D2107429301",
            "D2107404001",
            "D2107385701",
            "D2107346601",
            "D2107344001",
            "D2107300901",
            "D2107258401",
            "D2107254801",
            "D2107254701",
            "D2107222001",
            "D2107221901",
            "D2107200001",
            "D2107198901",
            "D2107191201",
            "D2107138401",
            "D2107072601",
            "D2107054401",
            "D2106943301",
            "D2106898601",
            "D2106876501",
            "D2106826601",
            "D2106801301",
            "D2106718601",
            "D2106718401",
            "D2106690801",
            "D2106684101",
            "D2106677501",
            "D2106677401",
            "D2106664701",
            "D2106652901",
            "D2106647901",
            "D2106632701",
            "D2106632401",
            "D2106628901",
            "D2106589001",
            "D2106580601",
            "D2106566001",
            "D2106528001",
            "D2106527801",
            "D2106515701",
            "D2106341401",
            "D2106189301",
            "D2106157601"
    );
}
