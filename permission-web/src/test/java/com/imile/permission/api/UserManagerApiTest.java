package com.imile.permission.api;

import com.alibaba.fastjson.JSONArray;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.MenuPermissionApiDTO;
import com.imile.permission.api.dto.PermissionApiDTO;
import com.imile.permission.api.dto.UserRoleApiDTO;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.groovy.GroovyParamJsonProperties;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
class UserManagerApiTest {

    @Test
    void bindingDataPermission() throws Exception {
        UserManagerApi userManagerApi = getUserManagerApi();
        String dataPermissionDTOList = GroovyParamJsonProperties.getProperty("dataPermissionDTOList");
        userManagerApi.bindingDataPermission("2103478901", JSONArray.parseArray(dataPermissionDTOList, DataPermissionApiDTO.class));
        RpcResult<PermissionApiDTO> bindingPermission = userManagerApi.getBindingPermission("90001");
        System.out.println(bindingPermission);
    }

    @Test
    void bindingMenuPermission() throws Exception {

        UserManagerApi userManagerApi = getUserManagerApi();
        String dataPermissionDTOList = GroovyParamJsonProperties.getProperty("menuIdList");
        userManagerApi.bindingMenuPermission("90001", JSONArray.parseArray(dataPermissionDTOList, MenuPermissionApiDTO.class));
        RpcResult<PermissionApiDTO> bindingPermission = userManagerApi.getBindingPermission("90001");
        System.out.println(bindingPermission);
    }

    @Test
    void bindingPermission() {

    }

    @Test
    void getBindingPermission() {
        UserManagerApi userManagerApi = getUserManagerApi();
        RpcResult<PermissionApiDTO> bindingPermission = userManagerApi.getBindingPermission("90001");
        System.out.println(bindingPermission);
    }


    @Test
    void test() {
        RpcResult<Boolean> booleanRpcResult = getUserManagerApi().bindingRoleForUser("2103478901", Arrays.asList(1L));
        System.out.println(booleanRpcResult);
    }


    @Test
    public void testRemoveRole() {
        RpcResult<Boolean> booleanRpcResult = getUserManagerApi().removeRole("2103478901", Arrays.asList(1L));
        System.out.println(booleanRpcResult);
    }

    @Test
    public void testAddRole() {
        RpcResult<Boolean> booleanRpcResult = getUserManagerApi().addRole("2103478901", Arrays.asList(1L));
        System.out.println(booleanRpcResult);
    }

    @Test
    void testBindingDataPermission() throws Exception {
        UserManagerApi userManagerApi = getUserManagerApi();
        DataPermissionApiDTO dataPermissionApiDTO = new DataPermissionApiDTO();
        dataPermissionApiDTO.setTypeCode(BusinessConstant.BASEDEPTDATA);
        dataPermissionApiDTO.setDataCodeList(Arrays.asList("1033204"));
        RpcResult<Boolean> booleanRpcResult = userManagerApi.bindingDataPermission("2103478901", Arrays.asList(dataPermissionApiDTO));
        System.out.println(booleanRpcResult);
    }

    @Test
    void testBindingMenuPermission() throws Exception {
        UserManagerApi userManagerApi = getUserManagerApi();
        MenuPermissionApiDTO menuPermissionApiDTO = new MenuPermissionApiDTO();
        menuPermissionApiDTO.setMenuId(-1129038186413174784L);
        menuPermissionApiDTO.setMenuIdList(Arrays.asList(
                -1129038186413174784L,
                1214225107816423424L,
                1129068298588397569L,
                1129039225547796481L,
                1214225241669246977L
        ));

        menuPermissionApiDTO.setSystemPlatform("iMile");
        RpcResult<Boolean> booleanRpcResult = userManagerApi.bindingMenuPermission("2103478901", Arrays.asList(menuPermissionApiDTO));
        System.out.println(booleanRpcResult);
    }

    @Test
    void testEditUserRole() {
        UserManagerApi userManagerApi = getUserManagerApi();
        UserRoleApiDTO userRoleApiDTO = new UserRoleApiDTO();
        userRoleApiDTO.setUserCode("2103478901");
        userRoleApiDTO.setRemoveIdList(
                Arrays.asList(
                        202112010007L,
                        202112010006L,
                        202112010003L
                )
        );
        userRoleApiDTO.setAddIdList(
                Arrays.asList(
                        202112010007L,
                        202112010008L,
                        202112010014L,
                        202112010017L,
                        202112010029L,
                        202112010030L
                )
        );
        RpcResult<Boolean> booleanRpcResult = userManagerApi.editUserRole(userRoleApiDTO);
        System.out.println(booleanRpcResult);
    }

    public UserManagerApi getUserManagerApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<UserManagerApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("userManagerApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        referenceConfig.setUrl("dubbo://localhost:18108");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(UserManagerApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }
}