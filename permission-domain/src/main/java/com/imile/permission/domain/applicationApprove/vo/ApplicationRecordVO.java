package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 角色申请审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
public class ApplicationRecordVO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 角色编号
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 英文描述
     */
    private String descriptionEn;

    /**
     * 是否接入审批流
     */
    private Integer isAccessApprovalFlow;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 描述nak
     */
    private String description;

    /**
     * 关联系统
     */
    @JsonIgnore
    private String multipleSystem;

    @JsonIgnore
    private String roleCountry;


    /**
     * 关联系统
     */
    private List<String> systemCodeList;

    /**
     * 0: 业务角色
     * 1: 默认角色
     */
    private Integer roleType;

    /**
     * 用功类型
     */
    private List<String> employeeTypeList;

    /**
     * 国家列表
     */
    private List<String> roleCountryList;

    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

}
