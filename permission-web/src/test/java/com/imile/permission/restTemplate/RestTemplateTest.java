package com.imile.permission.restTemplate;

import com.imile.common.result.Result;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataTreeVO;
import com.imile.permission.util.RestTemplateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
public class RestTemplateTest {

    @Test
    void exchange() {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer 27479983-3915-40d1-9a73-f1997f671316");
        HttpEntity<Object> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<Result> exchange = restTemplate.exchange("https://dev-ehr.52imile.cn/hrms/ent/dept/new/tree", HttpMethod.GET, requestEntity, Result.class);
        Result body = exchange.getBody();
        Object resultObject = body.getResultObject();
        System.out.println(body);
    }

    @Test
    void test() {
        List<BusinessBasicDataTreeVO> list = RestTemplateUtil.exeGet("https://dev-ehr.52imile.cn/hrms/ent/dept/new/tree", "27479983-3915-40d1-9a73-f1997f671316", BusinessBasicDataTreeVO.class);
        System.out.println(list);
    }
}
