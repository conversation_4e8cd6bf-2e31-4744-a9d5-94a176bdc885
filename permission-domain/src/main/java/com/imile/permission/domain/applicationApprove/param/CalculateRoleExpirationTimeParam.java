package com.imile.permission.domain.applicationApprove.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CalculateRoleExpirationTimeParam {

    /**
     * 有效时间（单位，天）
     */
    @NotNull(message = "effectiveTimeDay 不能为空")
    private Integer effectiveTimeDay;

    /**
     * 角色编号
     */
    @NotNull(message = "roleId 不能为空")
    private Long roleId;

    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

}
