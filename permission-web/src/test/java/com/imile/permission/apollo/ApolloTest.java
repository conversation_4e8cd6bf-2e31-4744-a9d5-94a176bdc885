package com.imile.permission.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2023/11/2
 */
public class ApolloTest {

    @Test
    public void testGetConfig() {
        // apollo 地址
        System.setProperty("apollo.meta", "http://*********:8081");
        // 命名空间
        System.setProperty("app.id", "permission");
        // 环境
        System.setProperty("env", "DEV");
        // 配置文件名
        String appId = "application";
        Config config = ConfigService.getConfig(appId);
        System.out.println(config);
    }

}
