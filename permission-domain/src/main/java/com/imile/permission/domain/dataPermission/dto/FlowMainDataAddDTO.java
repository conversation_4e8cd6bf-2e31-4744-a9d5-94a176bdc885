package com.imile.permission.domain.dataPermission.dto;

import com.imile.permission.domain.dataPermission.param.DynamicDataNestedParam;
import com.imile.permission.domain.dataPermission.param.RuleDataNestedParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Data
public class FlowMainDataAddDTO {
    /**
     * 系统
     */
    private String systemCode;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    private String dataUrl;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 动态函数部分
     */
    private List<DynamicDataNestedParam> dataList;

    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;


}
