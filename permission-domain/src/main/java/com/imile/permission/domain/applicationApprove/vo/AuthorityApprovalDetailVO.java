package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
public class AuthorityApprovalDetailVO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 角色编号
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 有效时间（单位，天）
     */
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 审批创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approveCreateTime;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approvedTime;


    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 权限类型（1:角色）
     */
    private Integer permissionType;

    /**
     * 审批流 ID
     */
    private Long approvalId;

    /**
     * 单据编号
     */
    private String applicationCode;


    /**
     * 置灰数据，用户、角色权限列表
     */
    private List<String> beforeDeptIdLIst;

    /**
     * 高亮数据，本次新申请的数据
     */
    private List<String> currentApplicationDeptIdList;

    /**
     * 菜单树
     */
    private List<MenuTreeVO> menuTree;

    /**
     * 流程id
     */
    private Long workCenterId;

    private String systemCode;

    private String roleCountry;

    /**
     * 一级分类
     */
    private String navigateName;

    /**
     * 二级分类
     */
    private String moduleName;
}
