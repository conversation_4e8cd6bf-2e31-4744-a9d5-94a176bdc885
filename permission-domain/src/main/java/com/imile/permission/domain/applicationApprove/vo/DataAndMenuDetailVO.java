package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import com.imile.permission.domain.applicationApprove.dto.BeforeAndCurrentDataListDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
public class DataAndMenuDetailVO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 有效时间（单位，天）
     */
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 审批创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approveCreateTime;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approvedTime;


    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 权限类型（1:角色）
     */
    private Integer permissionType;

    /**
     * 审批流 ID
     */
    private Long approvalId;

    /**
     * 单据编号
     */
    private String applicationCode;


    /**
     * 主数据的高亮与置灰数据
     */
    private List<BeforeAndCurrentDataListDTO> beforeAndCurrentDataListDTOS;

    /**
     * 流程id
     */
    private Long workCenterId;

    private String systemCode;

    private List<ResSystemResourceTreeApiDTO> resSystemResourceTreeApiDTOS;

    @Data
    public class ResSystemResourceTreeApiDTO implements Serializable {
        private String nodeId;
        private String title;
        private Boolean expand = false;
        private String parentNodeId;
        private String resourceNodeType;
        private List<Long> parentIds;
        private String resourceCode;
        private List<ResSystemResourceTreeApiDTO> children = new ArrayList();
        private Integer sensitiveLevel;

    }
}
