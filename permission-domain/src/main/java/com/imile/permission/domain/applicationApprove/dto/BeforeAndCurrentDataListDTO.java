package com.imile.permission.domain.applicationApprove.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BeforeAndCurrentDataListDTO {
    /**
     * 类型 typeCode
     */
    private String typeCode;

    /**
     * 置灰数据，用户、角色权限列表
     */
    private List<String> beforeDataList;

    /**
     * 高亮数据，本次新申请的数据
     */
    private List<String> currentApplicationDataList;

}
