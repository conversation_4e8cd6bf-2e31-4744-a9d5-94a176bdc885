<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.PermissionReclaimMessageLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.PermissionReclaimMessageLogDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="system_code" property="systemCode" />
        <result column="inactive_days" property="inactiveDays" />
        <result column="receive_time" property="receiveTime" />
        <result column="processed" property="processed" />
        <result column="login_time" property="loginTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_code, system_code, inactive_days, receive_time, processed, login_time, remark
    </sql>

    <insert id="insertOrUpdate">
        INSERT INTO permission_reclaim_message_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="log.id != null">id, </if>
            <if test="log.createDate != null">create_date, </if>
            <if test="log.createUserCode != null">create_user_code, </if>
            <if test="log.createUserName != null">create_user_name, </if>
            <if test="log.lastUpdDate != null">last_upd_date, </if>
            <if test="log.lastUpdUserCode != null">last_upd_user_code, </if>
            <if test="log.lastUpdUserName != null">last_upd_user_name, </if>
            <if test="log.userCode != null">user_code, </if>
            <if test="log.systemCode != null">system_code, </if>
            <if test="log.inactiveDays != null">inactive_days, </if>
            <if test="log.receiveTime != null">receive_time, </if>
            <if test="log.processed != null">processed, </if>
            <if test="log.loginTime != null">login_time, </if>
            <if test="log.remark != null">remark, </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="log.id != null">#{log.id,jdbcType=BIGINT},</if>
            <if test="log.createDate != null">#{log.createDate,jdbcType=TIMESTAMP},</if>
            <if test="log.createUserCode != null">#{log.createUserCode,jdbcType=VARCHAR},</if>
            <if test="log.createUserName != null">#{log.createUserName,jdbcType=VARCHAR},</if>
            <if test="log.lastUpdDate != null">#{log.lastUpdDate,jdbcType=TIMESTAMP},</if>
            <if test="log.lastUpdUserCode != null">#{log.lastUpdUserCode,jdbcType=VARCHAR},</if>
            <if test="log.lastUpdUserName != null">#{log.lastUpdUserName,jdbcType=VARCHAR},</if>
            <if test="log.userCode != null">#{log.userCode,jdbcType=VARCHAR},</if>
            <if test="log.systemCode != null">#{log.systemCode,jdbcType=VARCHAR},</if>
            <if test="log.inactiveDays != null">#{log.inactiveDays,jdbcType=INTEGER},</if>
            <if test="log.receiveTime != null">#{log.receiveTime,jdbcType=TIMESTAMP},</if>
            <if test="log.processed != null">#{log.processed,jdbcType=INTEGER},</if>
            <if test="log.loginTime != null">#{log.loginTime,jdbcType=TIMESTAMP},</if>
            <if test="log.remark != null"> #{log.remark,jdbcType=VARCHAR}, </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim prefix="" suffixOverrides=",">
            is_delete = 0,
            <if test="log.lastUpdDate != null">last_upd_date = VALUES(last_upd_date), </if>
        </trim>
    </insert>
</mapper>
