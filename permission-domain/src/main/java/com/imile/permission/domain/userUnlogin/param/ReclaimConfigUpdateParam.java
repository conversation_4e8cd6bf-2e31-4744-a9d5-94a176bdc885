package com.imile.permission.domain.userUnlogin.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/7/14
 */
@Data
public class ReclaimConfigUpdateParam{

    @NotBlank(message = "userType不能为空")
    private String userType;

    @NotBlank(message = "systemCode不能为空")
    private String systemCode;

    @NotNull(message = "thresholdDays不能为空")
    private Integer thresholdDays;

    @NotBlank(message = "strategyType不能为空")
    private String strategyType;

}
