package com.imile.permission.api;

import com.imile.hermes.enterprise.api.EntOcApi;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.permission.util.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConfigCenterConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/17
 */
public class EntOcApiTest {


    @Test
    public void test() {
        EntOcApi countryApi = getEntOcApi();
        RpcResult<List<EntOcApiDTO>> ocByCodes = countryApi.getOcByCodes(10L, Arrays.asList("S2101262001"));
        List<EntOcApiDTO> process = RpcResultProcessor.process(ocByCodes);
        EntOcApiDTO entOcApiDTO = process.get(0);
        String ocType = entOcApiDTO.getOcType();
        System.out.println(entOcApiDTO);


    }

    public EntOcApi getEntOcApi() {
        // EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<EntOcApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("EntOcApiTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://10.20.0.1:2181"));
        // Map<String, String> parameters = new HashMap<>();
        // parameters.put("include.spring.env", "false");
        // parameters.put("timeout", "1000000");
        // referenceConfig.setParameters(parameters);
        // referenceConfig.setUrl("dubbo://**********:18108");
        // referenceConfig.setUrl("dubbo://**********:18085");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(EntOcApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        // referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        ConfigCenterConfig configCenterConfig = new ConfigCenterConfig();
        configCenterConfig.setTimeout(10000000L);
        ConfigManager.getInstance().setConfigCenter(
                configCenterConfig
        );
        return referenceConfig.get();
    }

}
