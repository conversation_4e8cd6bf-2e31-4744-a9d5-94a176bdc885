<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.DirectAuthorizationApprovalDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.DirectAuthorizationApprovalDetailDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="user_id" property="userId" />
        <result column="source_type" property="sourceType" />
        <result column="source_id" property="sourceId" />
        <result column="application_date" property="applicationDate" />
        <result column="expiration_date" property="expirationDate" />
        <result column="operation_type" property="operationType" />
        <result column="operator" property="operator" />
        <result column="operation_time" property="operationTime" />
        <result column="remark" property="remark" />
        <result column="application_code" property="applicationCode" />
        <result column="approval_id" property="approvalId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <insert id="patchSave">
        insert into direct_authorization_approval_detail (id,create_date,create_user_code,create_user_name,last_upd_date,last_upd_user_code,last_upd_user_name,user_code,user_id,source_type,source_id,application_date,expiration_date,operation_type,operator,operation_time,remark,application_code,approval_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
(#{item.id}, #{item.createDate}, #{item.createUserCode}, #{item.createUserName}, #{item.lastUpdDate}, #{item.lastUpdUserCode}, #{item.lastUpdUserName}, #{item.userCode}, #{item.userId}, #{item.sourceType}, #{item.sourceId}, #{item.applicationDate}, #{item.expirationDate}, #{item.operationType}, #{item.operator}, #{item.operationTime}, #{item.remark}, #{item.applicationCode}, #{item.approvalId})
        </foreach>
    </insert>

</mapper>
