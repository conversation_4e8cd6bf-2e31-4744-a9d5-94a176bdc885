package com.imile.permission.data.base;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;

import java.util.List;

public class RoleResourceExcelListener implements ReadListener<RoleResourceExcel> {

    public List<RoleResourceExcel> cachedDataList = ListUtils.newArrayListWithExpectedSize(10000);

    public List<RoleResourceExcel> getCachedDataList() {
        return cachedDataList;
    }

    @Override
    public void invoke(RoleResourceExcel roleResourceExcel, AnalysisContext analysisContext) {
        cachedDataList.add(roleResourceExcel);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
