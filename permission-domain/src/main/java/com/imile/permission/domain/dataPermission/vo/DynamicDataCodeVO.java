package com.imile.permission.domain.dataPermission.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
public class DynamicDataCodeVO {

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据code
     */
    private String dataCode;

    /**
     * 数据接口 url
     */
    private String dataUrl;

    /**
     * 可选参数
     */
    private String optionalParameter;

    /**
     * 描述
     */
    private String description;

}
