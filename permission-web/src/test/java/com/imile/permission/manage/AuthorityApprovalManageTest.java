package com.imile.permission.manage;

import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.permission.ApplicationTest;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.dao.RoleAuthorityApprovalCollectDAO;
import com.imile.permission.domain.applicationApprove.query.AuthorityApprovalPageQuery;
import com.imile.permission.domain.applicationApprove.vo.ApplicationRecordVO;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalCollectDO;
import com.imile.permission.domain.entity.DirectAuthorizationApprovalDetailDO;
import com.imile.permission.domain.entity.RoleAuthorityApprovalCollectDO;
import com.imile.permission.domain.system.vo.SystemModuleVO;
import com.imile.permission.domain.system.vo.SystemNavigateVO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AuthorityApprovalManageTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    AuthorityApprovalManage authorityApprovalManage;


    @Test
    public void testGetApplicationRecordPage() {
        AuthorityApprovalPageQuery authorityApprovalPageQuery = new AuthorityApprovalPageQuery();
        LocalDateTime now = LocalDateTime.of(2024, 8, 8, 11, 37, 9);
        authorityApprovalPageQuery.setExpirationDate(now);
        authorityApprovalPageQuery.setCurrentPage(1);
        authorityApprovalPageQuery.setIsExpired(false);
        authorityApprovalPageQuery.setShowCount(20);
        authorityApprovalPageQuery.setUserCode("2103451701");
        PaginationResult<ApplicationRecordVO> applicationRecordPage = authorityApprovalManage.getApplicationRecordPage(authorityApprovalPageQuery);
        System.out.println(now);
    }


    @Test
    public void test() {
        List<DirectAuthorizationApprovalDetailDO> directDetailList = new ArrayList<>();
        DirectAuthorizationApprovalDetailDO item = new DirectAuthorizationApprovalDetailDO();

        item.setId(111L);
        item.setCreateDate(new Date());
        item.setCreateUserCode("1");
        item.setCreateUserName("1");
        item.setLastUpdDate(new Date());
        item.setLastUpdUserCode("1");
        item.setLastUpdUserName("");
        // item.setUserCode("");
        // item.setUserId();
        // item.setSourceType("");
        // item.setSourceId("");
        // item.setApplicationDate(LocalDateTime.now());
        // item.setExpirationDate(LocalDateTime.now());
        // item.setOperationType("");
        // item.setOperator("");
        // item.setOperationTime(LocalDateTime.now());
        // item.setRemark("");
        // item.setApplicationCode();
        // item.setApprovalId();

        directDetailList.add(item);
        authorityApprovalManage.saveDirectAuthorizationApprovalDetail(directDetailList);
    }

    @Test
    public void testUpdateRoleAuthorityApprovalCollectExpirationDateApplicationDate() {
        RoleAuthorityApprovalCollectDO collect1 = new RoleAuthorityApprovalCollectDO();
        collect1.setUserCode("2103595201");
        collect1.setRoleId(202112010023L);
        collect1.setApplicationDate(LocalDateTime.now());
        collect1.setExpirationDate(LocalDateTime.now());
        authorityApprovalManage.updateRoleAuthorityApprovalCollectExpirationDateApplicationDate(collect1);

        DirectAuthorizationApprovalCollectDO collect = new DirectAuthorizationApprovalCollectDO();
        collect.setUserCode("2103451701");
        collect.setSourceType("BaseDeptData");
        collect.setSourceId("1033204");
        collect.setApplicationDate(LocalDateTime.now());
        collect.setExpirationDate(LocalDateTime.now());
        authorityApprovalManage.updateDirectAuthorizationApprovalCollectExpirationDateApplicationDate(collect);
    }


    @Test
    public void testUpdateDirectAuthorizationApprovalCollectExpirationDateApplicationDate() {
        DirectAuthorizationApprovalCollectDO collect = new DirectAuthorizationApprovalCollectDO();
        authorityApprovalManage.updateDirectAuthorizationApprovalCollectExpirationDateApplicationDate(collect);
    }

    @Test
    public void saveBatchRoleAuthorityApprovalCollect() {
        List<RoleAuthorityApprovalCollectDO> list = Lists.newArrayList();
        RoleAuthorityApprovalCollectDO collectDO = new RoleAuthorityApprovalCollectDO();
        collectDO.setId(IdWorker.getId());
        collectDO.setRoleId(1155581291752865792L);
        collectDO.setUserCode("2103472001");
        collectDO.setApplicationDate(LocalDateTime.now());
        collectDO.setExpirationDate(BusinessConstant.MAX_EXPIRATION_DATE);

        list.add(collectDO);
        authorityApprovalManage.saveBatchRoleAuthorityApprovalCollect(list);
    }

    @Test
    public void systemNavigate() {
        SystemNavigateVO systemNavigateVO = new SystemNavigateVO();
        systemNavigateVO.setNavigateNameCn("人力资源");
        systemNavigateVO.setNavigateName("人力资源");
        systemNavigateVO.setNavigateNameEn("Human Resources");

        List<SystemModuleVO> moduleNavigateVOList = Lists.newArrayList();
        SystemModuleVO systemModuleVO = new SystemModuleVO();
        systemModuleVO.setSystemCode("HRMS");
        systemModuleVO.setModuleName("绩效管理");
        systemModuleVO.setModuleNameCn("绩效管理");
        systemModuleVO.setModuleNameEn("Performance Management");
        systemModuleVO.setModelType(2);
        systemModuleVO.setParentCode("HRperformanceManagement");

        SystemModuleVO systemModuleVO1 = new SystemModuleVO();
        systemModuleVO1.setSystemCode("HRMS");
        systemModuleVO1.setModelType(2);
        systemModuleVO1.setModuleName("假勤管理");
        systemModuleVO1.setModuleNameCn("假勤管理");
        systemModuleVO1.setModuleNameEn("Leave and Attendance Management");
        systemModuleVO1.setParentCode("HRAttendanceManage");

        SystemModuleVO systemModuleVO2 = new SystemModuleVO();
        systemModuleVO2.setSystemCode("HRMS");
        systemModuleVO2.setModelType(2);
        systemModuleVO2.setModuleName("组织管理");
        systemModuleVO2.setModuleNameCn("组织管理");
        systemModuleVO2.setModuleNameEn("Organization Management");
        systemModuleVO2.setParentCode("HROrganizationManage");

        SystemModuleVO systemModuleVO3 = new SystemModuleVO();
        systemModuleVO3.setSystemCode("HRMS");
        systemModuleVO3.setModelType(2);
        systemModuleVO3.setModuleName("人员培训");
        systemModuleVO3.setModuleNameCn("人员培训");
        systemModuleVO3.setModuleNameEn("Training Management");
        systemModuleVO3.setParentCode("HRTrainManagement");

        SystemModuleVO systemModuleVO4 = new SystemModuleVO();
        systemModuleVO4.setSystemCode("HRMS");
        systemModuleVO4.setModelType(2);
        systemModuleVO4.setModuleName("招聘管理");
        systemModuleVO4.setModuleNameCn("招聘管理");
        systemModuleVO4.setModuleNameEn("Recruitment Management");
        systemModuleVO4.setParentCode("HRRecruitmentManage");

        SystemModuleVO systemModuleVO5 = new SystemModuleVO();
        systemModuleVO5.setSystemCode("HRMS");
        systemModuleVO5.setModelType(2);
        systemModuleVO5.setModuleName("人员管理");
        systemModuleVO5.setModuleNameCn("人员管理");
        systemModuleVO5.setModuleNameEn("Staff Information Management");
        systemModuleVO5.setParentCode("HRPersonManage");

        SystemModuleVO systemModuleVO6 = new SystemModuleVO();
        systemModuleVO6.setSystemCode("HRMS");
        systemModuleVO6.setModelType(2);
        systemModuleVO6.setModuleName("账号管理");
        systemModuleVO6.setModuleNameCn("账号管理");
        systemModuleVO6.setModuleNameEn("System Admin Management");
        systemModuleVO6.setParentCode("HRAccount");

        SystemModuleVO systemModuleVO7 = new SystemModuleVO();
        systemModuleVO7.setSystemCode("HRMS");
        systemModuleVO7.setModelType(2);
        systemModuleVO7.setModuleName("薪酬管理");
        systemModuleVO7.setModuleNameCn("薪酬管理");
        systemModuleVO7.setModuleNameEn("Salary Management");
        systemModuleVO7.setParentCode("HRSalaryManage");

        moduleNavigateVOList.add(systemModuleVO);
        moduleNavigateVOList.add(systemModuleVO1);
        moduleNavigateVOList.add(systemModuleVO2);
        moduleNavigateVOList.add(systemModuleVO3);
        moduleNavigateVOList.add(systemModuleVO4);
        moduleNavigateVOList.add(systemModuleVO5);
        moduleNavigateVOList.add(systemModuleVO6);
        moduleNavigateVOList.add(systemModuleVO7);


        systemNavigateVO.setModuleNavigateVOList(moduleNavigateVOList);
        systemNavigateVO.setModuleNavigateVOList(moduleNavigateVOList);

        System.out.println(JSON.toJSONString(systemNavigateVO));
    }

    public static void main(String[] args) {
        List<SystemNavigateVO> systemNavigateVOList = Lists.newArrayList();
        SystemNavigateVO systemNavigateVO = new SystemNavigateVO();
        systemNavigateVO.setNavigateNameCn("运营中心");
        systemNavigateVO.setNavigateName("运营中心");
        systemNavigateVO.setNavigateNameEn("Operation Center");

        List<SystemModuleVO> moduleNavigateVOList = Lists.newArrayList();
        SystemModuleVO systemModuleVO = new SystemModuleVO();
        systemModuleVO.setSystemCode("iMile");
        systemModuleVO.setModuleName("DS Portal");
        systemModuleVO.setModuleNameCn("DS门户");
        systemModuleVO.setModuleNameEn("DS门户");
        systemModuleVO.setModelType(2);
        systemModuleVO.setParentCode("DSPortal");

        SystemModuleVO systemModuleVO1 = new SystemModuleVO();
        systemModuleVO1.setSystemCode("iMile");
        systemModuleVO1.setModuleName("Driver Portal");
        systemModuleVO1.setModuleNameCn("骑手门户");
        systemModuleVO1.setModuleNameEn("Driver Portal");
        systemModuleVO1.setModelType(2);
        systemModuleVO1.setParentCode("DriverPortal");

        SystemModuleVO systemModuleVO2 = new SystemModuleVO();
        systemModuleVO2.setSystemCode("iMile");
        systemModuleVO2.setModuleName("PDA Portal");
        systemModuleVO2.setModuleNameCn("PDA门户");
        systemModuleVO2.setModuleNameEn("PDA Portal");
        systemModuleVO2.setModelType(2);
        systemModuleVO2.setParentCode("PDA");

        SystemModuleVO systemModuleVO3 = new SystemModuleVO();
        systemModuleVO3.setSystemCode("iMile");
        systemModuleVO3.setModuleName("DC portal");
        systemModuleVO3.setModuleNameCn("DC门户");
        systemModuleVO3.setModuleNameEn("DC portal");
        systemModuleVO3.setModelType(2);
        systemModuleVO3.setParentCode("DCportal");

        SystemModuleVO systemModuleVO4 = new SystemModuleVO();
        systemModuleVO4.setSystemCode("iMile");
        systemModuleVO4.setModuleName("Center portal");
        systemModuleVO4.setModuleNameCn("中心门户");
        systemModuleVO4.setModuleNameEn("Center portal");
        systemModuleVO4.setModelType(2);
        systemModuleVO4.setParentCode("Centerportal");

        SystemModuleVO systemModuleVO5 = new SystemModuleVO();
        systemModuleVO5.setSystemCode("iMile");
        systemModuleVO5.setModuleName("ILMS portal");
        systemModuleVO5.setModuleNameCn("国际门户");
        systemModuleVO5.setModuleNameEn("ILMS portal");
        systemModuleVO5.setModelType(2);
        systemModuleVO5.setParentCode("ILMSPortal");

        SystemModuleVO systemModuleVO6 = new SystemModuleVO();
        systemModuleVO6.setSystemCode("iMile");
        systemModuleVO6.setModuleName("CS portal");
        systemModuleVO6.setModuleNameCn("CS门户");
        systemModuleVO6.setModuleNameEn("CS portal");
        systemModuleVO6.setModelType(2);
        systemModuleVO6.setParentCode("CSPortal");

        SystemModuleVO systemModuleVO7 = new SystemModuleVO();
        systemModuleVO7.setSystemCode("iMile");
        systemModuleVO7.setModuleName("Vendor Portal");
        systemModuleVO7.setModuleNameCn("供应商门户");
        systemModuleVO7.setModuleNameEn("Vendor portal");
        systemModuleVO7.setModelType(2);
        systemModuleVO7.setParentCode("VendorPortal");

        SystemModuleVO systemModuleVO8 = new SystemModuleVO();
        systemModuleVO8.setSystemCode("iMile");
        systemModuleVO8.setModuleName("OpCenter Portal");
        systemModuleVO8.setModuleNameCn("运营中心门户");
        systemModuleVO8.setModuleNameEn("OpCenter Portal");
        systemModuleVO8.setModelType(2);
        systemModuleVO8.setParentCode("OpCenterPortal");

        SystemModuleVO systemModuleVO9 = new SystemModuleVO();
        systemModuleVO9.setSystemCode("iMile");
        systemModuleVO9.setModuleName("Global Ticket");
        systemModuleVO9.setModuleNameCn("工单系统");
        systemModuleVO9.setModuleNameEn("Global Ticket");
        systemModuleVO9.setModelType(2);
        systemModuleVO9.setParentCode("GlobalTicket");

        moduleNavigateVOList.add(systemModuleVO);
        moduleNavigateVOList.add(systemModuleVO1);
        moduleNavigateVOList.add(systemModuleVO2);
        moduleNavigateVOList.add(systemModuleVO3);
        moduleNavigateVOList.add(systemModuleVO4);
        moduleNavigateVOList.add(systemModuleVO5);
        moduleNavigateVOList.add(systemModuleVO6);
        moduleNavigateVOList.add(systemModuleVO7);
        moduleNavigateVOList.add(systemModuleVO8);
        moduleNavigateVOList.add(systemModuleVO9);

        systemNavigateVO.setModuleNavigateVOList(moduleNavigateVOList);


        SystemNavigateVO systemNavigateVO1 = new SystemNavigateVO();
        systemNavigateVO1.setNavigateNameCn("Human Resources");
        systemNavigateVO1.setNavigateName("人力资源");
        systemNavigateVO1.setNavigateNameEn("Human Resources");

        List<SystemModuleVO> hrNavigateVOList = Lists.newArrayList();
        SystemModuleVO hrModuleVO = new SystemModuleVO();
        hrModuleVO.setSystemCode("HRMS");
        hrModuleVO.setModuleName("HRMS");
        hrModuleVO.setModuleNameCn("HR系统");
        hrModuleVO.setModuleNameEn("HRMS");
        hrModuleVO.setModelType(1);

        hrNavigateVOList.add(hrModuleVO);
        systemNavigateVO1.setModuleNavigateVOList(hrNavigateVOList);

        systemNavigateVOList.add(systemNavigateVO);
        systemNavigateVOList.add(systemNavigateVO1);
        System.out.println("system=====" + JSON.toJSONString(systemNavigateVOList));
    }


}
