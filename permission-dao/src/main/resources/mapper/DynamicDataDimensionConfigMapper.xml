<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.DynamicDataDimensionConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.DynamicDataDimensionConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="single_system" property="singleSystem" />
        <result column="dimension" property="dimension" />
        <result column="data_structures" property="dataStructures" />
        <result column="data_url" property="dataUrl" />
        <result column="type_code" property="typeCode" />
        <result column="type_name" property="typeName" />
        <result column="type_name_en" property="typeNameEn" />
        <result column="use_case_description" property="useCaseDescription" />
        <result column="multi_dimension_config" property="multiDimensionConfig" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, single_system, dimension, data_structures, data_url, type_code, type_name, type_name_en, use_case_description, multi_dimension_config
    </sql>

</mapper>
