package com.imile.permission.data.ds;

import com.alibaba.excel.EasyExcel;
import com.imile.idwork.IdWorkerUtil;
import com.imile.permission.data.pms.excel.DemoData;
import com.imile.permission.data.pms.excel.DemoDataListener;
import org.junit.jupiter.api.Test;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class DSTest {

    @Test
    public void test() throws Exception {
        String fileName = "D:/excel/MEX剩余网点司机权限.xlsx";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        DemoDataListener demoDataListener = new DemoDataListener();
        EasyExcel.read(fileName, DemoData.class, demoDataListener).sheet().doRead();
        List<DemoData> cachedDataList = demoDataListener.getCachedDataList();
        String s = "('%s', 'g', 'U:%s', 'R:%s'),";


        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\ds\\1.txt";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        cachedDataList.forEach(x -> {
            try {
                bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), x.getV0(), x.getV1()));
                bufferedWriter.newLine();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        bufferedWriter.flush();
        bufferedWriter.close();
    }

    public BufferedWriter getBufferedWriter(String outFile) throws Exception {
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8);
        BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter);
        return bufferedWriter;
    }

}
