package com.imile.permission.mq.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.imile.hrms.api.base.component.DifferField;
import com.imile.hrms.api.user.param.UserEventParam;
import com.imile.permission.constants.BusinessConstant;
import com.imile.permission.manage.RefactoringPermissionCasbinRuleManage;
import com.imile.permission.mq.basic.MqMsgBusinessProcessor;
import com.imile.permission.mq.basic.MsgType;
import com.imile.permission.service.UserAuthorizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class HrmsBaseInfoChangeEventProcessor implements MqMsgBusinessProcessor {


    @Value("#{'${recycling.system}'.split(',')}")
    private List<String> recyclingSystem;

    @Autowired
    private UserAuthorizationService userAuthorizationService;

    @Autowired
    private RefactoringPermissionCasbinRuleManage refactoringPermissionCasbinRuleManage;


    @Override
    public MsgType getMsgType() {
        return MsgType.HRMS_BASE_INFO_CHANGE_EVENT;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void execute(MessageExt message) {
        UserEventParam<UserEventParam.BaseInfoChange> entry = JSON.parseObject(new String(message.getBody()),
                new TypeReference<UserEventParam<UserEventParam.BaseInfoChange>>() {
                });
        String userCode = entry.getUserCode();
        log.info("HrmsBaseInfoChangeEventProcessor | execute | userCode {}", userCode);
        UserEventParam.BaseInfoChange body = entry.getBody();
        if (Objects.isNull(body)) {
            return;
        }

        DifferField postIdDifferField = null;
        DifferField deptIdDifferField = null;
        List<DifferField> differFields = Optional.ofNullable(body.getChangeFieldList()).orElse(Lists.newArrayList());
        for (DifferField differField : differFields) {
            if (BusinessConstant.POST_ID.equals(differField.getFieldName())) {
                postIdDifferField = differField;
            } else if (BusinessConstant.DEPT_ID.equals(differField.getFieldName())) {
                deptIdDifferField = differField;
            }
        }

        Boolean recyclingFlag = Boolean.FALSE;
        if (Objects.isNull(postIdDifferField) || postIdDifferField.getAfterValue().equals(postIdDifferField.getBeforeValue())) {
            log.info("HrmsBaseInfoChangeEventProcessor | execute | userCode {} postId no change, post {}", userCode, JSON.toJSONString(postIdDifferField));
        } else {
            Long newPostId = Long.valueOf((Integer) postIdDifferField.getAfterValue());

            // 删除旧岗位
            Long userPostId = refactoringPermissionCasbinRuleManage.getPostIdByUserCode(userCode);
            if (Objects.nonNull(userPostId)) {
                refactoringPermissionCasbinRuleManage.removeUserPost(userCode, userPostId);
            }
            // 添加新岗位
            refactoringPermissionCasbinRuleManage.addUserPost(userCode, newPostId);
            recyclingFlag = Boolean.TRUE;

        }

        if (Objects.isNull(deptIdDifferField) || deptIdDifferField.getAfterValue().equals(deptIdDifferField.getBeforeValue())) {
            log.info("HrmsBaseInfoChangeEventProcessor | execute | userCode {} deptId no change, dept {}", userCode, JSON.toJSONString(deptIdDifferField));
        } else {
            recyclingFlag = Boolean.TRUE;
        }

        // 用户的岗位、部门信息变更，更新权限
        if (recyclingFlag) {
            userAuthorizationService.recyclingSystemPermission(userCode, recyclingSystem);
        }
    }

}
