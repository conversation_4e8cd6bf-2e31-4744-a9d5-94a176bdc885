package com.imile.permission.domain.dataPermission.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@Data
public class DataEqualRightsDTO {

    /**
     * 同权的系统来源
     */
    private String sourceSystem;

    /**
     * 数据字典类型
     */
    private String typeCode;

    /**
     * 数据字典名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String typeName;

    /**
     * 字典数据
     */
    private List<String> dataCodeList;
}
