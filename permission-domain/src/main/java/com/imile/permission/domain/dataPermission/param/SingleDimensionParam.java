package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
public class SingleDimensionParam {
    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;
    /**
     * 数据接口 url
     */
    private String dataUrl;
    /**
     * 请求类型
     */
    private String requestType;
    /**
     * 数据类型
     */
    private String typeCode;
    /**
     * 数据类型名称
     */
    private String typeName;
    /**
     * 数据类型英文名称
     */
    private String typeNameEn;
    /**
     * 系统code
     */
    private String systemCode;


    /**
     * 使用场景解释
     */
    private String useCaseDescription;
    /**
     * 动态函数部分
     */
    private List<DynamicDataNestedParam> dataList;
    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;

    /**
     * 映射部分
     */
    private List<MappingFieldInfoParam> fieldInfos;

}
