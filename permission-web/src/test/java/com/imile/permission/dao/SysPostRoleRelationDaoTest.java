package com.imile.permission.dao;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import org.junit.jupiter.api.BeforeAll;

/**
 * <AUTHOR>
 * @since 2023/11/17
 */
public class SysPostRoleRelationDaoTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }


}
