package com.imile.permission.domain.permission.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/8/1
 */
@Data
public class ApprovalCollectDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关联ID
     */
    private String relateId;

    /**
     *  权限类型
     */
    private String permissionType;

    /**
     * 名称
     */
    private String name;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 申请日期
     */
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    private LocalDateTime expirationDate;
}
