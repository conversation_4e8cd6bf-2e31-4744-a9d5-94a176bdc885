package com.imile.permission.domain.workCenter.param;

import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Data
public class NodePermissionParam {
    /**
     * 工作中心ID
     */
    private Long workCenterId;
    /**
     * sourceNodeId
     */
    private String sourceNodeId;

    /**
     * 操作权限
     */
    private List<MenuPermissionDTO> menuPermissionDTOList;

    /**
     * 数据权限
     */
    private List<DataPermissionDTO> dataPermissionDTOList;

}
