package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@Data
public class FilterCheckDataVO {

    private String id;

    private String name;

    private List<FilterCheckDataVO> children;

    /**
     * 是否选中
     */
    private Boolean checked = false;

    /**
     * 扩展标签
     */
    private List<String> extensionTagCodeList;

    private String status = "ACTIVE";


    private String var1;

    private String var2;

    private String var3;
}
