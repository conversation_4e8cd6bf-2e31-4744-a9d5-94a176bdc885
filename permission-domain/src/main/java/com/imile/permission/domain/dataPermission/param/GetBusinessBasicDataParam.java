package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/13
 */
@Data
public class GetBusinessBasicDataParam {

    /**
     * 系统code
     */
    private List<String> systemCodeList;

    /**
     * 查询场景的类型
     * 1-角色；2-员工；
     * @see com.imile.permission.enums.PermissionSceneTypeEnum
     */
    private Integer sceneType = 1;
}
