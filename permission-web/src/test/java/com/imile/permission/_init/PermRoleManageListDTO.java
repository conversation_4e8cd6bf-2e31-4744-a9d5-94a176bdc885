package com.imile.permission._init;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/5/20
 */
@Data
public class PermRoleManageListDTO {

    private Long id;
    /**
     * 系统组织id
     */
    private Long orgId;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 所属营运组织id
     */
    private Long ocId;
    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;
    /**
     * 所属部门id
     */
    private Long depId;
    /**
     * 是否客户角色
     */
    private Integer isClientRole;
    /**
     * 角色类型
     */
    private String type;
    /**
     * 是否只允许超管编辑(1是 0否)
     */
    private Integer isAdminUpdate;
    /**
     * 描述
     */
    private String desc;
    /**
     * 是否系统创建 0-否，1-是
     */
    private Integer isSysCreate;
    /**
     * 版本编码
     */
    private Long recordVersion;
    /**
     * 新增时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 新增用户编码
     */
    private String createUserCode;
    /**
     * 新增用户名
     */
    private String createUserName;
    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;
    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;
    /**
     * 最后修改用户名
     */
    private String lastUpdUserName;

    private String typeName;

    private Integer postSize = 0;
}
