package com.imile.permission.domain.applicationApprove.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-28
 * @version: 1.0
 */
@Data
public class ApprovalCreateRoleUserDTO {
    /**
     * 取值对象名称
     */
    private String fetchObjectName;

    /**
     * 取值对象
     */
    private String fetchObject;

    /**
     * 取值对象对应字段值
     */
    private String fetchObjectValue;

    /**
     * 取值对象对应字段值名称
     */
    private String fetchObjectValueName;

    /**
     * 审批角色名称
     */
    private String approvalRoleName;

    /**
     * 审批角色
     */
    private String approvalRole;


    /**
     * userCode
     */
    private List<String> approvalUserCodes;


    /**
     * userCode
     */
    private List<ApprovalUserInfoDTO> approvalUserInfos = Lists.newArrayList();
}
