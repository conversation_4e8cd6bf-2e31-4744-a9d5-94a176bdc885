package com.imile.permission.domain.dataPermission.param;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * KA商家list
 * <AUTHOR>
 * @since 2024/9/19
 */
@Data
public class KAClientParam extends BaseQuery {

    private List<String> clientCodeList;

    /**
     * 商家id列表
     */
    private List<Long> ids;

    /**
     * 名称和编码模糊查询
     */
    private String nameAndCodeLike;
}
