package com.imile.permission.api;

import com.imile.permission.api.dto.UserDataPermissionApiDTO;
import com.imile.permission.api.dto.UserWithSystemApiDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConfigCenterConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
public class UserDataPermissionApiTest {


    @Test
    public void test() {
        UserDataPermissionApi userDataPermissionApi = getUserDataPermissionApi();
        UserWithSystemApiDTO userWithSystemApiDTO = new UserWithSystemApiDTO();
        userWithSystemApiDTO.setUserCode("2103561001");
        userWithSystemApiDTO.setSystemCode("Prism(iMile)");

        RpcResult<UserDataPermissionApiDTO> userDataPermission = userDataPermissionApi.getUserDataPermission(userWithSystemApiDTO);
        System.out.println(userDataPermission);

    }

    public UserDataPermissionApi getUserDataPermissionApi() {
        // EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<UserDataPermissionApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("UserDataPermissionApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        // Map<String, String> parameters = new HashMap<>();
        // parameters.put("include.spring.env", "false");
        // parameters.put("timeout", "1000000");
        // referenceConfig.setParameters(parameters);
        referenceConfig.setUrl("dubbo://localhost:18108");
        // referenceConfig.setUrl("dubbo://**********:18085");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(UserDataPermissionApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        // referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        ConfigCenterConfig configCenterConfig = new ConfigCenterConfig();
        configCenterConfig.setTimeout(10000000L);
        ConfigManager.getInstance().setConfigCenter(
                configCenterConfig
        );
        return referenceConfig.get();
    }

}
