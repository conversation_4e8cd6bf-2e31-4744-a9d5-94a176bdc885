package com.imile.permission.api;

import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
public class PermissionManagerApiTest {

    @Test
    public void testAddDataPermission() {
        PermissionManagerApi permissionManagerApi = getPermissionManagerApi();
        permissionManagerApi.addDataPermission("dept", "100", "103");
        permissionManagerApi.addDataPermission("dept", "100", "104");
        permissionManagerApi.addDataPermission("dept", "100", "105");
        permissionManagerApi.addDataPermission("dept", "100", "106");
        permissionManagerApi.addDataPermission("dept", "100", "107");
        permissionManagerApi.addDataPermission("dept", "100", "110");
        permissionManagerApi.addDataPermission("dept", "110", "111");
        permissionManagerApi.addDataPermission("dept", "110", "112");
    }


    @Test
    public void changeDataPermission() {
        PermissionManagerApi permissionManagerApi = getPermissionManagerApi();
        permissionManagerApi.changeDataPermission("dept", "101", "900", Arrays.asList("901", "902"));
    }

    public static PermissionManagerApi getPermissionManagerApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<PermissionManagerApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("PermissionManagerApiTest"));

        // 3.设置服务注册中心
        //referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        referenceConfig.setUrl("dubbo://localhost:18108");


        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(PermissionManagerApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }
}
