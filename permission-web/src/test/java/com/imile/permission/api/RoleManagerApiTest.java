package com.imile.permission.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.imile.common.page.PaginationResult;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.MenuPermissionApiDTO;
import com.imile.permission.api.dto.ResSystemResourceTreeApiDTO;
import com.imile.permission.api.dto.RoleAddApiDTO;
import com.imile.permission.api.dto.RoleBasicInfoApiDTO;
import com.imile.permission.api.dto.RoleDetailApiDTO;
import com.imile.permission.api.dto.RoleUpdateApiDTO;
import com.imile.permission.api.dto.RoleUpdateBasicInfoApiDTO;
import com.imile.permission.api.dto.RoleUpdatePermissionApiDTO;
import com.imile.permission.api.query.RoleApiQuery;
import com.imile.permission.groovy.GroovyParamJsonProperties;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConfigCenterConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
class RoleManagerApiTest {

    @Test
    void addRole() throws Exception {
        String property = GroovyParamJsonProperties.getProperty("roleAddApiDTO");
        RoleAddApiDTO roleAddApiDTO = JSONObject.parseObject(property, RoleAddApiDTO.class);
        //roleAddApiDTO.setUserCode("1");
        //roleAddApiDTO.setUserName("1");
        RpcResult<Long> rpcResult = getRoleManagerApi().addRole(roleAddApiDTO);
        //RpcResult<RoleDetailApiDTO> roleDetailApiDTORpcResult = getRoleManagerApi().roleDetail(rpcResult.getResult());
        System.out.println(rpcResult);
    }

    @Test
    void updateRole() throws Exception {
        String property = GroovyParamJsonProperties.getProperty("roleUpdateApiDTO");
        RoleUpdateApiDTO roleUpdateApiDTO = JSONObject.parseObject(property, RoleUpdateApiDTO.class);
        roleUpdateApiDTO.setUserCode("1");
        roleUpdateApiDTO.setUserName("1");
        RpcResult<Boolean> rpcResult = getRoleManagerApi().updateRole(roleUpdateApiDTO);
        System.out.println(rpcResult);
    }

    @Test
    void roleDetail() {
        RpcResult<RoleDetailApiDTO> roleDetailApiDTORpcResult = getRoleManagerApi().roleDetail(1743902217612894210L);
        System.out.println(roleDetailApiDTORpcResult);
    }

    @Test
    void updateRoleBasicInfo() throws Exception {
        String property = GroovyParamJsonProperties.getProperty("roleUpdateBasicInfoApiDTO");
        RoleUpdateBasicInfoApiDTO roleUpdateApiDTO = JSONObject.parseObject(property, RoleUpdateBasicInfoApiDTO.class);
        roleUpdateApiDTO.setUserCode("2");
        roleUpdateApiDTO.setUserName("2");
        RpcResult<Boolean> rpcResult = getRoleManagerApi().updateRoleBasicInfo(roleUpdateApiDTO);
        System.out.println(rpcResult);
    }

    @Test
    void updateRoleMenuPermission() throws Exception {
        String menuPermissionDTOList = GroovyParamJsonProperties.getProperty("menuPermissionDTOList");
        RpcResult<Boolean> rpcResult = getRoleManagerApi().updateRoleMenuPermission(1736590454711463937L, JSONArray.parseArray(menuPermissionDTOList, MenuPermissionApiDTO.class), 10L, "admin", "admin");
        System.out.println(rpcResult);
    }

    @Test
    void updateRoleDataPermission() throws Exception {
        String dataPermissionApiDTOList = GroovyParamJsonProperties.getProperty("dataPermissionApiDTOList");
        RpcResult<Boolean> rpcResult = getRoleManagerApi().updateRoleDataPermission(1736590454711463937L, JSONArray.parseArray(dataPermissionApiDTOList, DataPermissionApiDTO.class), 10L, "admin", "admin");
        System.out.println(rpcResult);
    }

    @Test
    void updateRolePermission() throws Exception {
        String property = GroovyParamJsonProperties.getProperty("roleUpdatePermissionApiDTO");
        RoleUpdatePermissionApiDTO roleUpdateApiDTO = JSONObject.parseObject(property, RoleUpdatePermissionApiDTO.class);
        roleUpdateApiDTO.setUserCode("1");
        roleUpdateApiDTO.setUserName("1");
        RpcResult<Boolean> rpcResult = getRoleManagerApi().updateRolePermission(roleUpdateApiDTO);
        System.out.println(rpcResult);
    }

    @Test
    void deleteRole() throws Exception {
        RpcResult<Boolean> rpcResult = getRoleManagerApi().deleteRole(1743241296912404481L, 10L, "admin", "admin");
        System.out.println(rpcResult);
    }

    @Test
    void roleList() {
        RoleApiQuery roleApiQuery = new RoleApiQuery();
        RpcResult<List<RoleBasicInfoApiDTO>> listRpcResult = getRoleManagerApi().roleList(roleApiQuery);
        System.out.println(listRpcResult);
    }

    @Test
    void rolePage() {
        RoleApiQuery roleApiQuery = new RoleApiQuery();
        RpcResult<PaginationResult<RoleBasicInfoApiDTO>> paginationResultRpcResult = getRoleManagerApi().rolePage(roleApiQuery);
        System.out.println(paginationResultRpcResult);
    }


    @Test
    void getTreeByRoleId() throws Exception {
        RpcResult<List<ResSystemResourceTreeApiDTO>> zhCn = getRoleManagerApi().getTreeByRoleId(1L, 10L, "zh_CN");
        List<ResSystemResourceTreeApiDTO> result = zhCn.getResult();
        System.out.println(result);
    }

    @Test
    void updateRoleDisable() throws Exception {
        getRoleManagerApi().updateRoleDisable(1L,0,"admin","admin");

    }


    @Test
    public void test1() {
        RpcResult<RoleDetailApiDTO> roleDetailApiDTORpcResult = getRoleManagerApi().roleDetail(1896894776505610242L);
        RoleDetailApiDTO result = roleDetailApiDTORpcResult.getResult();
        List<DataPermissionApiDTO> dataPermissionDTOList = result.getDataPermissionDTOList();
        System.out.println(dataPermissionDTOList);
        System.out.println(roleDetailApiDTORpcResult);

    }

    public RoleManagerApi getRoleManagerApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<RoleManagerApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("RoleManagerApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
       referenceConfig.setUrl("dubbo://localhost:18108");
        //referenceConfig.setUrl("dubbo://*************:18108");
        //referenceConfig.setUrl("dubbo://***********:18108");
        // referenceConfig.setUrl("dubbo://***********:18108");

        ConfigCenterConfig configCenterConfig = new ConfigCenterConfig();
        configCenterConfig.setTimeout(10000000L);
        ConfigManager.getInstance().setConfigCenter(
                configCenterConfig
        );


        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(RoleManagerApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }
}