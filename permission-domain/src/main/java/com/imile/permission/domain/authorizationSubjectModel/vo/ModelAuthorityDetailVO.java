package com.imile.permission.domain.authorizationSubjectModel.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/28
 */
@Data
public class ModelAuthorityDetailVO {

    /**
     * 授权主体模型类型编码
     */
    private String subjectModelCode;
    /**
     * 主体编码
     */
    private String mainId;

    /**
     * 主体权限详情
     */
    private List<MainTypeCodeVO> mainTypeCodeList;

}
