package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
@Data
public class UserMenuVO {

    private String userCode;

    /**
     * 系统平台名称
     */
    private String systemCode;

    /**
     * 根节点菜单 ID
     */
    private Long menuId;

    /**
     * 已拥有的菜单idList
     */
    private List<Long> menuIdList;

    /**
     * 申请中的菜单idList
     */
    private List<UserApplyMenuVO> userApplyMenuVOList;

    @Data
    public static class UserApplyMenuVO {

        private List<Long> applyIdList;

        private Long approveId;

        private Integer approvalStatus;
    }
}


