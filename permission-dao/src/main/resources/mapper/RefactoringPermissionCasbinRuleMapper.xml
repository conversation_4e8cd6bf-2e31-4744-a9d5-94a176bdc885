<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.RefactoringPermissionCasbinRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.RefactoringPermissionCasbinRuleDO">
        <id column="id" property="id"/>
        <result column="ptype" property="ptype"/>
        <result column="v0" property="v0"/>
        <result column="v1" property="v1"/>
        <result column="v2" property="v2"/>
        <result column="v3" property="v3"/>
        <result column="v4" property="v4"/>
        <result column="v5" property="v5"/>
    </resultMap>
    <insert id="patchAdd">
        INSERT INTO refactoring_permission_casbin_rule(id,ptype, v0, v1, v2, v3, v4, v5) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.ptype}, #{item.v0}, #{item.v1}, #{item.v2}, #{item.v3}, #{item.v4}, #{item.v5})
        </foreach>
    </insert>

    <select id="getContainV2ForV0" resultType="java.lang.String">
        select v0
        from refactoring_permission_casbin_rule
        <where>
            ptype = 'p'
            <if test="v1!=null and v1!='' ">
                and v1 = #{v1}
            </if>

            <if test="v2List!=null and v2List.size()>0">
                and v2 in
                <foreach collection="v2List" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by v0
        having count(distinct v2) >= ${v2List.size}
    </select>
    <select id="getExistV2ForV0" resultType="java.lang.String">
        select distinct v0
        from refactoring_permission_casbin_rule
        <where>
            ptype = 'p'
            <if test="v1!=null and v1!='' ">
                and v1 = #{v1}
            </if>

            <if test="v2List!=null and v2List.size()>0">
                and v2 in
                <foreach collection="v2List" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getDataPermissionDuplicateData" resultType="java.lang.Long">
        select id
        from refactoring_permission_casbin_rule
        <where>
            ptype = 'p'
            <if test="v0List!=null and v0List.size()>0">
                and v0 in
                <foreach collection="v0List" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and v1 = #{v1}
            group by v0, v1, v2
            having count(*) >1
        </where>
    </select>
    <select id="getGV0LikeV1InCount" resultType="com.imile.permission.domain.casbin.dto.V1CountDTO">
        select v1, count(*) as count
        from refactoring_permission_casbin_rule
        where ptype = 'g'
        and v1 in
        <foreach collection="v1List" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and v0 like #{v0}
        group by v1
        ;

    </select>


    <delete id="removePolicy">
        delete from refactoring_permission_casbin_rule
        <where>
            ptype = 'p'
            and v0 in
            <foreach collection="v0List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="v1!=null and v1!='' ">
                and v1 = #{v1}
            </if>
            and v2 in
            <foreach collection="v2List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </delete>

    <update id="updateNotPolicy">
        update refactoring_permission_casbin_rule
        set v3 = '!'
        <where>
            ptype = 'p'
            and v0 in
            <foreach collection="v0List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="v1!=null and v1!='' ">
                and v1 = #{v1}
            </if>
            and v2 in
            <foreach collection="v2List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <delete id="removePolicyV1V2">
        delete from refactoring_permission_casbin_rule
        <where>
            ptype = 'p'
            and v0 in
            <foreach collection="v0List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and v1 in
            <foreach collection="v1List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </delete>

    <select id="getPolicyV1V2" resultType="com.imile.permission.domain.entity.RefactoringPermissionCasbinRuleDO">
        select id, ptype, v0, v1, v2, v3, v4, v5
        from refactoring_permission_casbin_rule
        <where>
            ptype = 'p'
            <if test="v0List!=null and v0List.size()>0">
                and v0 in
                <foreach collection="v0List" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and v1 = #{v1}
        </where>
    </select>

    <select id="getPV0ByV1" resultType="java.lang.String">
        select distinct v0
        from refactoring_permission_casbin_rule
        where ptype = 'p'
          and v1 = #{v1}
    </select>

    <select id="getPV0ByV1V3" resultType="java.lang.String">
        select v0
        from refactoring_permission_casbin_rule
        where ptype = 'p'
        <if test="v1!=null and v1!='' ">
            and v1 = #{v1,jdbcType=VARCHAR}
        </if>
        <if test="v3!=null and v3!='' ">
            and v3 = #{v3,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getExistDataCodes" resultType="java.lang.String">
        select v2
        from refactoring_permission_casbin_rule
        where ptype = 'p'
        <if test="v0!=null and v0!='' ">
            and v0= #{v0,jdbcType=VARCHAR}
        </if>
        <if test="v1!=null and v1!='' ">
            and v1 = #{v1,jdbcType=VARCHAR}
        </if>
        <if test="v2List!=null and v2List.size()>0">
            and v2 in
            <foreach collection="v2List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getExistMenuId" resultType="java.lang.String">
        select v3
        from refactoring_permission_casbin_rule
        where ptype = 'p'
        <if test="v0!=null and v0!='' ">
            and v0= #{v0,jdbcType=VARCHAR}
        </if>
        <if test="v3List!=null and v3List.size()>0">
            and v3 in
            <foreach collection="v3List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getDuplication" resultType="com.imile.permission.domain.dataProcessing.DuplicationDTO">
        select
            ptype,
            v0,
            v1,
            v2,
            v3,
            v4,
            v5,
            group_concat(id) as idListStr
        from
            refactoring_permission_casbin_rule
        group by
            ptype,
            v0,
            v1,
            v2,
            v3,
            v4,
            v5
        having
            count(*) > 1
        limit #{limitSize}
    </select>

    <select id="getPV0ByV3ListLikeV0" resultType="java.lang.String">
        select v0
        from refactoring_permission_casbin_rule
        where ptype = 'p'
        <if test="v3List!=null and v3List.size()>0">
            and v3 in
            <foreach collection="v3List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="v0Like != null and v0Like != ''">
            and v0 like #{v0Like,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="listUserCodeByRole" resultType="java.lang.String">
        select distinct SUBSTRING(v0, 3)
        from refactoring_permission_casbin_rule
        where ptype = 'g'
        and v0 like 'U:%'
        <if test="v1List!=null and v1List.size()>0">
            and v1 in
            <foreach collection="v1List" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
