package com.imile.permission.domain.dataPermission.vo;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/24
 */
public class MainDataFieldMappingTreeVO {

    @JsonIgnore
    private Map<String, Object> fields = new LinkedHashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getFields() {
        return fields;
    }

    @JsonAnySetter
    public void setField(String key, Object value) {
        fields.put(key, value);
    }

}
