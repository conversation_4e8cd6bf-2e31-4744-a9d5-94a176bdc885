package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class BusinessBasicDataPermissionConfigAddParam {

    /**
     * 数据结构类型（0:列表 1:树）
     */
    @NotNull(message = "数据结构不能为空")
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    @NotBlank(message = "数据URL不能为空")
    private String dataUrl;


    @NotBlank(message = "请求类型不能为空")
    private String requestType;

    /**
     * 数据类型
     */
    @NotBlank(message = "权限类型不能为空")
    private String typeCode;

    /**
     * 数据类型名称
     */
    @NotBlank(message = "权限类型名称不能为空")
    private String typeName;

    /**
     * 数据类型名称
     */
    private String typeNameEn;

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 来源系统
     */
    private String refSourceSystem;

    /**
     * 来源数据类型
     */
    private String sourceTypeCode;

    /**
     * 应用类型
     * owner 创建
     * ref 引用
     */
    private String useCaseType;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 动态函数部分
     */
    private List<DynamicDataNestedParam> dataList;


    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;

    /**
     * 数据扩展部分
     */
    @Valid
    private List<ExtensionTagParam> extensionTagList;


    private List<MappingFieldInfoParam> fieldInfos;

}
