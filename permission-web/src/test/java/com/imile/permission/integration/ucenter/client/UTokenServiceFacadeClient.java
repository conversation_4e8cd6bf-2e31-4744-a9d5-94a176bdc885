package com.imile.permission.integration.ucenter.client;

import com.imile.rpc.common.RpcResult;
import com.imile.ucenter.api.UTokenServiceFacade;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/1/23
 */
public class UTokenServiceFacadeClient {


    @Test
    public void test(){
        UTokenServiceFacade utoken = getUtoken();
        RpcResult<UserInfoDTO> aaa = utoken.queryUserBaseInfoByUToken("aaa");
        System.out.println(aaa);
    }

    public UTokenServiceFacade getUtoken() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<UTokenServiceFacade> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("DubboTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        //referenceConfig.setUrl("dubbo://localhost:21881");
        //referenceConfig.setUrl("dubbo://localhost:21882");


        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(UTokenServiceFacade.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }

}
