package com.imile.permission.domain.applicationApprove.dto;

import com.imile.permission.annotation.HyperLink;
import lombok.Data;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-11-18
 * @version: 1.0
 */
@Data
public class ApprovalUserInfoDTO {
    /**
     * 编码
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 图片url
     */
    //@HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;

    /**
     * 头像
     */
    private String profilePhotoUrlHttps;

    /**
     * 操作，赋值 APPROVING
     */
    private String approvalOperation;
}
