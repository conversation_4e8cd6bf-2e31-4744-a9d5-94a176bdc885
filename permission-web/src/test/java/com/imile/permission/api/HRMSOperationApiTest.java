package com.imile.permission.api;

import com.imile.permission.api.custom.HRMSOperationApi;
import com.imile.permission.api.dto.HRMSRoleMenuDTO;
import com.imile.permission.api.dto.HRMSUserRoleDTO;
import com.imile.permission.api.dto.MenuTreeDTO;
import com.imile.permission.api.dto.UserRoleApiDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
class HRMSOperationApiTest {

    @Test
    void listRoleByUserCodeTest() {
        HRMSOperationApi hrmsOperationApi = getHRMSOperationApi();
        RpcResult<List<HRMSUserRoleDTO>> listRpcResult = hrmsOperationApi.listRoleByUserCode(Arrays.asList("2103478901", "2103534701"));
        System.out.println(listRpcResult);

    }

    @Test
    void listMenuByRoleIdTest() {
        HRMSOperationApi hrmsOperationApi = getHRMSOperationApi();
        RpcResult<List<HRMSRoleMenuDTO>> listRpcResult = hrmsOperationApi.listMenuByRoleId(Arrays.asList(1856221847746617345L,1805442018437869570L, 1805505816665169921L));
        System.out.println(listRpcResult);
    }

    @Test
    void getMenuTreeTest() {
        HRMSOperationApi hrmsOperationApi = getHRMSOperationApi();
        RpcResult<List<MenuTreeDTO>> menuTree = hrmsOperationApi.getMenuTree();

    }

    public HRMSOperationApi getHRMSOperationApi() {
        // EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<HRMSOperationApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("HRMSOperationApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        referenceConfig.setUrl("dubbo://localhost:18108");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(HRMSOperationApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        // referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }
}