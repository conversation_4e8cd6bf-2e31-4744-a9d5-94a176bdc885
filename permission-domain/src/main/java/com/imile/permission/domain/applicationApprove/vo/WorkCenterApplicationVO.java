package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/6/22
 */
@Data
public class WorkCenterApplicationVO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 流程id
     */
    private Long workCenterId;

    /**
     * 角色名称
     */
    private String workCenterName;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 描述
     */
    private String description;
}
