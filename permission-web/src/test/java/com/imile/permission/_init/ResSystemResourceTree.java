package com.imile.permission._init;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统菜单树
 * Created by <PERSON><PERSON> on 2018/9/11.
 */
@Data
public class ResSystemResourceTree {
    /**
     * 节点ID
     */
    private String nodeId;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否展开
     */
    private Boolean expand = false;
    /**
     * 父节点ID
     */
    private String parentNodeId;

    /**
     * 菜单类型:RESOURCE_ROOT-根目录;RESOURCE_DIR-目录;RESOURCE_MENU-菜单;RESOURCE_COMPONENT-按钮,RESOURCE_ACTION-请求
     */
    private String resourceNodeType;

    /**
     * 父节点ID
     */
    private List<Long> parentIds;

    /**
     * 资源code
     */
    private String resourceCode;

    /**
     * 子节点
     */
    private List<ResSystemResourceTree> children = new ArrayList<ResSystemResourceTree>();

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setExpand(Boolean expand) {
        this.expand = expand;
    }

    public void setParentNodeId(String parentNodeId) {
        this.parentNodeId = parentNodeId;
    }

    public void setChildren(List<ResSystemResourceTree> children) {
        this.children = children;
    }
}
