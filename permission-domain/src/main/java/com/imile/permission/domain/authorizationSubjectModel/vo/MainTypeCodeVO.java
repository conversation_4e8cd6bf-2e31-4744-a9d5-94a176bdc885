package com.imile.permission.domain.authorizationSubjectModel.vo;

import com.imile.permission.domain.authorizationSubjectModel.dto.ModelAuthorityDataDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/28
 */
@Data
public class MainTypeCodeVO {
    /**
     * 数据类型
     */
    private String typeCode;
    /**
     * 数据值
     */
    private List<ModelAuthorityDataDTO> dataList;

}
