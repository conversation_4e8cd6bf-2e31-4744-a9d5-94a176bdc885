package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@Data
public class DataPermissionUpdateBaseInfoParam {
    /**
     * 数据类型
     */
    private String typeCode;


    /**
     * 使用场景解释
     */
    private String useCaseDescription;


    /**
     * 数据详情
     */
    private List<SimpleDataItemParam> dataItemList;
}
