package com.imile.permission.data.pda;

import com.alibaba.excel.EasyExcel;
import com.imile.idwork.IdWorkerUtil;
import com.imile.permission.data.base.RoleResourceExcel;
import com.imile.permission.data.base.RoleResourceExcelListener;
import com.imile.permission.data.base.UserRoleExcel;
import com.imile.permission.data.base.UserRoleExcelListener;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class PDATest {

    @Test
    public void test5() throws Exception {
        String file1 = "D:\\workspace\\edit\\txt\\22.txt";
        String file2 = "D:\\workspace\\edit\\txt\\23.txt";
        List<String> list1 = new ArrayList<>();
        BufferedReader b1 = new BufferedReader(new FileReader(file1));

        String line = b1.readLine();
        while (line != null) {
            list1.add(line);
            line = b1.readLine();
        }
        b1.close();

        List<String> list2 = new ArrayList<>();
        BufferedReader b2 = new BufferedReader(new FileReader(file2));
        line = b2.readLine();
        while (line != null) {
            list2.add(line);
            line = b2.readLine();
        }
        b2.close();

        System.out.println();
        Collection<String> strings = CollectionUtils.removeAll(list2, list1);
        System.out.println(strings);


        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\pda\\6.txt";
        // String s = "('%s', 'g', '%s', '%s'),";
        String s = "('%s', '0', '1', '2024-06-05 15:05:08', 'System', 'System', '2024-06-05 15:41:55', 'System', 'System', '%s', '%s', '2024-06-05 15:41:55', '2099-12-31 23:59:59', '0'),";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        strings.forEach(x -> {
            try {
                String[] split = x.split(",");
                bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), split[0], split[1]));
                bufferedWriter.newLine();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });


        bufferedWriter.flush();
        bufferedWriter.close();


    }

    @Test
    public void test4() throws Exception {
        String file1 = "D:\\workspace\\edit\\txt\\19.txt";
        String file2 = "D:\\workspace\\edit\\txt\\20.txt";
        List<String> list1 = new ArrayList<>();
        BufferedReader b1 = new BufferedReader(new FileReader(file1));

        String line = b1.readLine();
        while (line != null) {
            list1.add(line);
            line = b1.readLine();
        }
        b1.close();

        List<String> list2 = new ArrayList<>();
        BufferedReader b2 = new BufferedReader(new FileReader(file2));
        line = b2.readLine();
        while (line != null) {
            list2.add(line);
            line = b2.readLine();
        }
        b2.close();

        System.out.println();
        Collection<String> strings = CollectionUtils.removeAll(list2, list1);
        System.out.println(strings);


        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\pda\\5.txt";
        String s = "('%s', 'g', '%s', '%s'),";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        strings.forEach(x -> {
            try {
                String[] split = x.split(",");
                bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), split[0], split[1]));
                bufferedWriter.newLine();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });


        bufferedWriter.flush();
        bufferedWriter.close();


    }

    @Test
    public void test3() throws Exception {
        String fileName = "C:\\Users\\<USER>\\Documents\\WXWork\\1688857718516856\\Cache\\File\\2024-06\\PDA初始化权限数据（2024-06-05）.xlsx";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        RoleResourceExcelListener demoDataListener = new RoleResourceExcelListener();
        EasyExcel.read(fileName, RoleResourceExcel.class, demoDataListener).sheet(4).doRead();
        List<RoleResourceExcel> cachedDataList = demoDataListener.getCachedDataList();
        String s = "('%s', 'p', '%s', 'SP:iMile', 'RM:-1130547762920960001', '%s', '*', '#'),";


        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\pda\\4.txt";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        cachedDataList.forEach(x -> {
            try {
                bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), x.getV0().replaceAll("#", ""), x.getV1().replaceAll("#", "")));
                bufferedWriter.newLine();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });


        bufferedWriter.flush();
        bufferedWriter.close();

    }


    @Test
    public void test2() throws Exception {
        String fileName = "D:/excel/PDA初始化权限数据（新模板格式）.xlsx";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        RoleResourceExcelListener demoDataListener = new RoleResourceExcelListener();
        EasyExcel.read(fileName, RoleResourceExcel.class, demoDataListener).sheet(1).doRead();
        List<RoleResourceExcel> cachedDataList = demoDataListener.getCachedDataList();
        String s = "('%s', 'p', 'R:%s', 'SP:iMile', 'RM:-1130547762920960001', 'M:%s'),";


        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\pda\\2.txt";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        cachedDataList.forEach(x -> {
            try {
                bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), x.getV0().replaceAll("#", ""), x.getV3().replaceAll("#", "")));
                bufferedWriter.newLine();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        bufferedWriter.flush();
        bufferedWriter.close();
    }


    @Test
    public void test() throws Exception {
        String fileName = "D:/excel/PDA初始化权限数据（新模板格式）.xlsx";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        UserRoleExcelListener demoDataListener = new UserRoleExcelListener();
        EasyExcel.read(fileName, UserRoleExcel.class, demoDataListener).sheet(2).doRead();
        List<UserRoleExcel> cachedDataList = demoDataListener.getCachedDataList();
        String s = "('%s', 'g', 'U:%s', 'R:%s'),";


        String outFile = "D:\\workspace\\imile\\permission\\permission-web\\src\\test\\java\\com\\imile\\permission\\data\\pda\\1.txt";
        BufferedWriter bufferedWriter = getBufferedWriter(outFile);
        cachedDataList.forEach(x -> {
            try {
                bufferedWriter.write(String.format(s, IdWorkerUtil.getId(), x.getV0(), x.getV1().replaceAll("#", "")));
                bufferedWriter.newLine();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        bufferedWriter.flush();
        bufferedWriter.close();
    }

    public BufferedWriter getBufferedWriter(String outFile) throws Exception {
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8);
        BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter);
        return bufferedWriter;
    }

}
