package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/28
 */
@Data
public class MenuTreeVO implements Serializable {

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 资源编码
     */
    private String code;
    /**
     * 菜单名称
     */
    private String title;

    /**
     * 菜单类型
     */
    private String type;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 敏感等级， 1-敏感，0-不敏感
     */
    private Integer sensitiveLevel;

    /**
     * 授权状态，0-未拥有,1-已拥有，2-申请中
     */
    private Integer authStatus;

    /**
     * 全半选，0-未选中，1-全选，2-半选
     */
    private Integer checkStatus;

    /**
     * 是否可选
     */
    private Boolean canCheck;

    /**
     * 父节点id
     */
    private Long pid;

    /**
     * 子节点
     */
    private List<MenuTreeVO> children = new ArrayList<>();
}
