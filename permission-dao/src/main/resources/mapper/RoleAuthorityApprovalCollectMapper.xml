<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.RoleAuthorityApprovalCollectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.RoleAuthorityApprovalCollectDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="application_date" property="applicationDate" />
        <result column="expiration_date" property="expirationDate" />
        <result column="is_execute" property="isExecute" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_code, user_id, role_id, application_date, expiration_date, is_execute
    </sql>
    <select id="getApplicationRecordEffectiveCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        role_authority_approval_collect
        INNER JOIN sys_role ON role_authority_approval_collect.role_id = sys_role.id
        WHERE
        sys_role.is_delete = 0
        AND role_authority_approval_collect.is_delete = 0
        AND role_authority_approval_collect.user_code = #{userCode}
        and role_authority_approval_collect.expiration_date &gt; #{now}
    </select>
    <select id="getApplicationRecordLossCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            role_authority_approval_collect
                INNER JOIN sys_role ON role_authority_approval_collect.role_id = sys_role.id
        WHERE
            sys_role.is_delete = 0
          AND role_authority_approval_collect.is_delete = 0
          AND role_authority_approval_collect.user_code = #{userCode}
          and role_authority_approval_collect.expiration_date &lt;= #{now}
    </select>

    <select id="getUserRoleCollect" resultType="com.imile.permission.domain.applicationApprove.vo.UserRoleCollectVO">
        SELECT user_code, role_id, COUNT(*) as duplicate_count,GROUP_CONCAT(id) as duplicateIds
        FROM role_authority_approval_collect
        GROUP BY user_code, role_id
        HAVING COUNT(*) > 1
    </select>

    <insert id="batchUpsert">
        <foreach collection="collectDOList" item="item" separator=";">
            INSERT INTO role_authority_approval_collect
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id, </if>
                <if test="item.isDelete != null">is_delete, </if>
                <if test="item.recordVersion != null">record_version, </if>
                <if test="item.createDate != null">create_date, </if>
                <if test="item.createUserCode != null">create_user_code, </if>
                <if test="item.createUserName != null">create_user_name, </if>
                <if test="item.lastUpdDate != null">last_upd_date, </if>
                <if test="item.lastUpdUserCode != null">last_upd_user_code, </if>
                <if test="item.lastUpdUserName != null">last_upd_user_name, </if>
                <if test="item.userCode != null">user_code, </if>
                <if test="item.userId != null">user_id, </if>
                <if test="item.roleId != null">role_id, </if>
                <if test="item.applicationDate != null">application_date, </if>
                <if test="item.expirationDate != null">expiration_date, </if>
                <if test="item.isExecute != null">is_execute, </if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id}, </if>
                <if test="item.isDelete != null">#{item.isDelete}, </if>
                <if test="item.recordVersion != null">#{item.recordVersion}, </if>
                <if test="item.createDate != null">#{item.createDate}, </if>
                <if test="item.createUserCode != null">#{item.createUserCode}, </if>
                <if test="item.createUserName != null">#{item.createUserName}, </if>
                <if test="item.lastUpdDate != null">#{item.lastUpdDate}, </if>
                <if test="item.lastUpdUserCode != null">#{item.lastUpdUserCode}, </if>
                <if test="item.lastUpdUserName != null">#{item.lastUpdUserName}, </if>
                <if test="item.userCode != null">#{item.userCode}, </if>
                <if test="item.userId != null">#{item.userId}, </if>
                <if test="item.roleId != null">#{item.roleId}, </if>
                <if test="item.applicationDate != null">#{item.applicationDate}, </if>
                <if test="item.expirationDate != null">#{item.expirationDate}, </if>
                <if test="item.isExecute != null">#{item.isExecute}, </if>
            </trim>

            ON DUPLICATE KEY UPDATE
            <trim prefix="" suffixOverrides=",">
                <if test="item.applicationDate != null">application_date=VALUES(application_date), </if>
                <if test="item.expirationDate != null">expiration_date=VALUES(expiration_date), </if>
                <if test="item.lastUpdDate != null">last_upd_date = VALUES(last_upd_date), </if>
                <if test="item.lastUpdUserCode != null">last_upd_user_code = VALUES(last_upd_user_code), </if>
                <if test="item.lastUpdUserName != null">last_upd_user_name = VALUES(last_upd_user_name), </if>
            </trim>
        </foreach>
    </insert>

    <select id="getApprovalCollectDTOList" resultType="com.imile.permission.domain.permission.dto.ApprovalCollectDTO">
        select
        role_authority_approval_collect.id,
        role_authority_approval_collect.user_code,
        role_authority_approval_collect.user_id,
        role_authority_approval_collect.role_id as relateId,
        role_authority_approval_collect.application_date,
        role_authority_approval_collect.expiration_date,
        sys_role.role_name_en as name,
        sys_role.multiple_system as systemCode,
        sys_role.is_disable
        from role_authority_approval_collect
        inner join sys_role on sys_role.id = role_authority_approval_collect.role_id
        where role_authority_approval_collect.is_delete = 0
        <if test="query.startExpireTime != null">
            and role_authority_approval_collect.expiration_date &gt;= #{query.startExpireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.endExpireTime != null">
            and role_authority_approval_collect.expiration_date &lt;= #{query.endExpireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.relateIds != null and query.relateIds.size()>0">
            and role_authority_approval_collect.id in
            <foreach collection="query.relateIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="listSystemRoleByUserCode" resultType="com.imile.permission.domain.role.dto.UserRoleDTO">
        select role_authority_approval_collect.user_code,
        role_authority_approval_collect.role_id,
        sys_role.role_name,
        sys_role.role_name_en,
        sys_role.is_disable,
        sys_role.description,
        sys_role.description_en,
        role_authority_approval_collect.expiration_date
        from role_authority_approval_collect
        inner join sys_role on role_authority_approval_collect.role_id = sys_role.id
        where role_authority_approval_collect.is_delete = 0
        and sys_role.is_delete = 0
        and role_authority_approval_collect.user_code in
        <foreach item="userCode" index="index" collection="userCodeList"
                 open="(" separator="," close=")">
            #{userCode}
        </foreach>
        and
        <foreach item="item" index="index" collection="system"
                 open="(" separator=" or " close=")">
            JSON_CONTAINS(sys_role.multiple_system,JSON_ARRAY('${item}'))
        </foreach>
    </select>
</mapper>
