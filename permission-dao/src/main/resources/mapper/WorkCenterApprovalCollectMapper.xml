<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.WorkCenterApprovalCollectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.WorkCenterApprovalCollectDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="user_id" property="userId" />
        <result column="work_center_id" property="workCenterId" />
        <result column="application_date" property="applicationDate" />
        <result column="expiration_date" property="expirationDate" />
        <result column="is_execute" property="isExecute" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_code, user_id, work_center_id, application_date, expiration_date, is_execute
    </sql>

    <insert id="batchUpsert">
        <foreach collection="collectDOList" item="item" separator=";">
            INSERT INTO work_center_approval_collect
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id, </if>
                <if test="item.isDelete != null">is_delete, </if>
                <if test="item.recordVersion != null">record_version, </if>
                <if test="item.createDate != null">create_date, </if>
                <if test="item.createUserCode != null">create_user_code, </if>
                <if test="item.createUserName != null">create_user_name, </if>
                <if test="item.lastUpdDate != null">last_upd_date, </if>
                <if test="item.lastUpdUserCode != null">last_upd_user_code, </if>
                <if test="item.lastUpdUserName != null">last_upd_user_name, </if>
                <if test="item.userCode != null">user_code, </if>
                <if test="item.userId != null">user_id, </if>
                <if test="item.workCenterId != null">work_center_id, </if>
                <if test="item.applicationDate != null">application_date, </if>
                <if test="item.expirationDate != null">expiration_date, </if>
                <if test="item.isExecute != null">is_execute, </if>
            </trim>
             VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id}, </if>
                <if test="item.isDelete != null">#{item.isDelete}, </if>
                <if test="item.recordVersion != null">#{item.recordVersion}, </if>
                <if test="item.createDate != null">#{item.createDate}, </if>
                <if test="item.createUserCode != null">#{item.createUserCode}, </if>
                <if test="item.createUserName != null">#{item.createUserName}, </if>
                <if test="item.lastUpdDate != null">#{item.lastUpdDate}, </if>
                <if test="item.lastUpdUserCode != null">#{item.lastUpdUserCode}, </if>
                <if test="item.lastUpdUserName != null">#{item.lastUpdUserName}, </if>
                <if test="item.userCode != null">#{item.userCode}, </if>
                <if test="item.userId != null">#{item.userId}, </if>
                <if test="item.workCenterId != null">#{item.workCenterId}, </if>
                <if test="item.applicationDate != null">#{item.applicationDate}, </if>
                <if test="item.expirationDate != null">#{item.expirationDate}, </if>
                <if test="item.isExecute != null">#{item.isExecute}, </if>
            </trim>

            ON DUPLICATE KEY UPDATE
            <trim prefix="" suffixOverrides=",">
                <if test="item.applicationDate != null">application_date=VALUES(application_date), </if>
                <if test="item.expirationDate != null">expiration_date=VALUES(expiration_date), </if>
                <if test="item.lastUpdDate != null">last_upd_date = VALUES(last_upd_date), </if>
                <if test="item.lastUpdUserCode != null">last_upd_user_code = VALUES(last_upd_user_code), </if>
                <if test="item.lastUpdUserName != null">last_upd_user_name = VALUES(last_upd_user_name), </if>
                <!-- 注意：主键或唯一键字段不应在此处更新 -->
            </trim>
        </foreach>
    </insert>

    <select id="getApprovalCollectDTOList" resultType="com.imile.permission.domain.permission.dto.ApprovalCollectDTO">
        select
        work_center_approval_collect.id,
        work_center_approval_collect.user_code,
        work_center_approval_collect.user_id,
        work_center_approval_collect.work_center_id as relateId,
        work_center_approval_collect.application_date,
        work_center_approval_collect.expiration_date,
        work_center.work_center_name as name,
        work_center.single_system as systemCode
        from work_center_approval_collect
        inner join work_center on work_center.id = work_center_approval_collect.work_center_id
        where work_center_approval_collect.is_delete = 0
        <if test="query.startExpireTime != null">
            and work_center_approval_collect.expiration_date &gt;= #{query.startExpireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.endExpireTime != null">
            and work_center_approval_collect.expiration_date &lt;= #{query.endExpireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.relateIds != null and query.relateIds.size()>0">
            and work_center_approval_collect.id in
            <foreach collection="query.relateIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
