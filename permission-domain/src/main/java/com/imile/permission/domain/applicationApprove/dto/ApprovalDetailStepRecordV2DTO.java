package com.imile.permission.domain.applicationApprove.dto;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-28
 * @version: 1.0
 */
@Data
public class ApprovalDetailStepRecordV2DTO {


    /**
     * 是否展开审批信息 0: 否 1:是
     */
    private Integer isExpandApprovalStep;

    /**
     * 是否有子单 0: 否 1:是
     */
    private Integer hasChildInfo;

    /**
     * 流程详情(整体流程)
     */
    private List<ApprovalDetailStepRecordWholeDTO> approvalDetailStepRecordWholeDTOS;

    /**
     * 流程详情(子单)
     */
    private List<ApprovalDetailStepRecordChildDTO> approvalDetailStepRecordChildDTOS;

    /**
     * 该流程所有找不到的人员信息
     */
    private List<ApprovalPreviewErrorUserDTO> allApprovalPreviewErrorUserDTOList;


}
