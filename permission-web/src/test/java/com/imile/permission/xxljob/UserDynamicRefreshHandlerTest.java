package com.imile.permission.xxljob;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public class UserDynamicRefreshHandlerTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    UserDynamicRefreshHandler userDynamicRefreshHandler;

    @Test
    public void test() {
        userDynamicRefreshHandler.userDynamicRefresh("test");
        userDynamicRefreshHandler.userDynamicRefresh("test");
        userDynamicRefreshHandler.userDynamicRefresh("test");
    }

}
