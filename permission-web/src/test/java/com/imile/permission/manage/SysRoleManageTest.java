package com.imile.permission.manage;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/11/7
 */
class SysRoleManageTest extends ApplicationTest {



    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Test
    void addSysRole() {
    }

    @Test
    void updateSysRole() {
    }

    @Test
    void getById() {
    }

    @Test
    void selectSysRole() {
    }

    @Test
    void roleExist() {
    }
}