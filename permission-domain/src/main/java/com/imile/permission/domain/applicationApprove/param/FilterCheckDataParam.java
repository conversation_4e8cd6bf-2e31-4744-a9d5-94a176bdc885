package com.imile.permission.domain.applicationApprove.param;

import com.imile.permission.domain.dataPermission.dto.DataCodeExtensionTagDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@Data
public class FilterCheckDataParam {

    /**
     * 来源数据类型编码
     */
    @NotBlank(message = "主数据sourceTypeCode不可为空")
    private String sourceTypeCode;

    /**
     * 主数据 MAINDATA, 单维度数据 SINGLEDIMENSION
     */
    private String type;

    /**
     * 已选中的数据
     */
    private List<String> dataCodeList;

    /**
     * 已选中的数据
     */
    private List<DataCodeExtensionTagDTO> dataExtensionTagList;
}
