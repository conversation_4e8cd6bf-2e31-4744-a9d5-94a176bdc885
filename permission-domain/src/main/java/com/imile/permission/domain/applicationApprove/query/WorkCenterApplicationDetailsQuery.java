package com.imile.permission.domain.applicationApprove.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/4/7
 */
@Data
public class WorkCenterApplicationDetailsQuery {
    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * 用户编码
     */
    @NotNull(message = "roleId 不能为空")
    private Long workCenterId;

    /**
     * 是否进行时区转换，默认为 true
     */
    private Boolean useTimezone = true;
}
