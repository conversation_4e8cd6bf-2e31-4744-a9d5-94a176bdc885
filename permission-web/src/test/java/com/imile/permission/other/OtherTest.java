package com.imile.permission.other;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.imile.permission.domain.dataPermission.vo.IdNameVO;
import com.imile.permission.domain.role.query.SysRoleQuery;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
public class OtherTest {


    @Test
    public void arr() {
        List<Integer> urlEncodeDOList = Arrays.asList(9, 2, 1);
        Integer max = urlEncodeDOList.get(0);
        int[] codes = new int[max];
        for (Integer i : urlEncodeDOList) {
            codes[i - 1] = 1;
        }
        StringBuilder sb = new StringBuilder();
        for (int code : codes) {
            sb.append(code);
        }
        System.out.println(sb);
    }


    @Test
    public void testEq() {
        System.out.println("11".equals(null));
    }

    @Test
    public void testJson2() {
        // 创建一个Map
        Map<String, Object> map = new HashMap<>();
        map.put("name", "John");
        map.put("age", 30);
        map.put("email", "<EMAIL>");
        map.put("city", "New York");

        // 不使用排序特性
        String unsortedJson = JSON.toJSONString(map);
        System.out.println("Unsorted JSON: " + unsortedJson);

        // 使用排序特性
        String sortedJson = JSON.toJSONString(map, SerializerFeature.MapSortField);
        System.out.println("Sorted JSON: " + sortedJson);
    }

    @Test
    public void testJson() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "John");
        map.put("age", 30);
        String jsonString = JSON.toJSONString(JSON.parseObject("{\"name\":\"张三\",\"age\":18}"), SerializerFeature.MapSortField);
        String jsonString2 = JSON.toJSONString(map, SerializerFeature.MapSortField);
        System.out.println(jsonString);
    }

    @Test
    public void testL() {
        Long l = Long.valueOf("2000000000000024799");
        System.out.println(l);
    }


    @Test
    public void eq() {
        boolean equalCollection = CollectionUtils.isEqualCollection(Arrays.asList(2, 1, 2), Arrays.asList(1, 2, 2));
        System.out.println(equalCollection);
    }

    @Test
    public void testGetSystemPlatformJson() {
        SysRoleQuery sysRoleQuery = new SysRoleQuery();
        sysRoleQuery.setSystemPlatformList(Arrays.asList("HRMS"));
        String s = "JSON_CONTAINS(system_platform_json,"
                + "JSON_ARRAY( " + sysRoleQuery.getSystemPlatformJson() + ")" + ")";
        System.out.println(s);
    }


    @Test
    public void testDelayQueue() throws InterruptedException {


        TimeUnit.SECONDS.sleep(30L);
    }

    @Test
    public void testSubList() {
        ArrayList<Object> objects = new ArrayList<>();
        objects.add(1);
        objects.add(2);
        objects.add(3);
        objects.add(4);

        for (int i = 0; i < objects.size(); i++) {
            System.out.println(objects.subList(i, objects.size()));
        }
    }


    @Test
    public void putIfAbsent() {
        List<IdNameVO> result = new ArrayList<>();

        IdNameVO idNameVO = new IdNameVO();
        idNameVO.setId("id");
        idNameVO.setName("name");

        IdNameVO idNameVO2 = new IdNameVO();
        idNameVO2.setId("id2");
        idNameVO2.setName("name2");

        IdNameVO idNameVO3 = new IdNameVO();
        idNameVO3.setId("id2");
        idNameVO3.setName("name2");

        result.add(idNameVO);
        result.add(idNameVO2);
        result.add(idNameVO3);

        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        result = result.stream().filter(e -> Objects.isNull(objectObjectHashMap.putIfAbsent(e.getId(), e.getName()))).collect(Collectors.toList());

        System.out.println(result);


    }


    @Test
    public void post() {
        HttpResponse<String> response = Unirest.post("https://imiledelivery166155.protheus.cloudtotvs.com.br:4050/rest/IMINT07/ID007/1.0")
                .header("Content-Type", "application/json")
                .header("Authorization", "Basic aW50ZWdyYWNhb2ltaWxlQGltaWxlOiRpMm00aTZsOGUwJA==")
                .body("{\n    \"TipoForcecedor\": \"F\",\n    \"CNPJCPF\": \"2222\",\n    \"RazaoSocial\": \"topic1\",\n    \"NomeFantasia\": \"topic1\",\n    \"Endereco\": \"11111111111\",\n    \"Bairro\": \"area\",\n    \"Estado\": \"1011\",\n    \"CodMun\": \"bar-2\",\n    \"CEP\": \"333\",\n    \"Pais\": \"105\"\n}")
                .asString();
        String body = response.getBody();
        System.out.println(body);
    }

    @Test
    public void test2() {
        List<Integer> integers = new ArrayList<>();
        integers.forEach(
                e -> {
                    System.out.println(e);
                }
        );
    }

    @Test
    public void test() {
        List<Integer> integers = new ArrayList<>();
        integers.forEach(
                e -> {
                    System.out.println(e);
                }
        );
    }


}
