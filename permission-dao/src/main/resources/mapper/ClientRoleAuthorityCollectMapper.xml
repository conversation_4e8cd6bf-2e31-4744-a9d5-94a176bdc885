<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.ClientRoleAuthorityCollectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.ClientRoleAuthorityCollectDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="client_code" property="clientCode" />
        <result column="role_id" property="roleId" />
        <result column="application_date" property="applicationDate" />
        <result column="expiration_date" property="expirationDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, client_code, role_id, application_date, expiration_date
    </sql>
    <select id="getClientRole" resultType="com.imile.permission.domain.client.dto.ClientRoleRecordDTO">
        select client_role_authority_collect.client_code      as clientCode,
               sys_role.id                                    as roleId,
               sys_role.multiple_system                       as multipleSystem,
               sys_role.role_name                             as roleName,
               sys_role.role_name_en                          as roleNameEn,
               sys_role.description_en                        as descriptionEn,
               client_role_authority_collect.application_date as applicationDate,
               client_role_authority_collect.expiration_date  as expirationDate
        from client_role_authority_collect
                 inner join sys_role
                            on client_role_authority_collect.role_id = sys_role.id
        where client_role_authority_collect.is_delete = 0
          and sys_role.is_delete = 0
          and client_role_authority_collect.client_code = #{clientCode}

        <if test="!isExpired">
            and client_role_authority_collect.expiration_date &gt; #{expirationDate}
        </if>
        <if test="isExpired">
            and client_role_authority_collect.expiration_date &lt;= #{expirationDate}
        </if>
    </select>
    <select id="getRecordEffectiveCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            client_role_authority_collect
                INNER JOIN sys_role ON client_role_authority_collect.role_id = sys_role.id
        WHERE
            sys_role.is_delete = 0
          AND client_role_authority_collect.is_delete = 0
          AND client_role_authority_collect.client_code = #{clientCode}
          and client_role_authority_collect.expiration_date &gt; #{now}


    </select>
    <select id="getRecordLossCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            client_role_authority_collect
                INNER JOIN sys_role ON client_role_authority_collect.role_id = sys_role.id
        WHERE
            sys_role.is_delete = 0
          AND client_role_authority_collect.is_delete = 0
          AND client_role_authority_collect.client_code = #{clientCode}
          and client_role_authority_collect.expiration_date &lt;= #{now}


    </select>
    <select id="countRoleMap" resultType="com.imile.permission.domain.client.dto.ClientCountRoleDTO">
        select role_id as roleId, count(*) as count
        from client_role_authority_collect
        where is_delete = 0
            and expiration_date &gt; #{localDateTime}
            and role_id in
            <foreach collection="roleIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        group by role_id
    </select>
    <select id="getClientCodeByRoleId" resultType="java.lang.String">
        select client_code
        from client_role_authority_collect
        where role_id = #{roleId}
          and expiration_date &gt; #{now}
    </select>

</mapper>
