package com.imile.permission.domain.dataPermission.vo;

import com.imile.permission.domain.dataPermission.dto.DataEqualRightsDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SystemDataPermissionVO {

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 数据权限
     */
    private List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();

    /**
     * 同权的数据权限
     */
    private List<DataEqualRightsDTO> dataEqualRightsDTOList = new ArrayList<>();
}
