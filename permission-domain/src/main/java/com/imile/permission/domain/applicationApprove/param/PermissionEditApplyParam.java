package com.imile.permission.domain.applicationApprove.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
@Accessors(chain = true)
public class PermissionEditApplyParam {

    /**
     * 审批流 ID
     */
    @NotNull(message = "approvalId 不能为空")
    private Long approvalId;

    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * 用户编号
     */
    @NotNull(message = "userId 不能为空")
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID")
    private Long deptId;

    /**
     * 角色编号
     */
    @NotNull(message = "roleId 不能为空")
    private Long roleId;

    /**
     * 有效时间（单位，天）
     */
    @NotNull(message = "effectiveTimeDay 不能为空")
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    @NotNull(message = "reason 不能为空")
    private String reason;

    /**
     * 业务类型
     */
    // @NotNull(message = "businessType 不能为空")
    private String businessType;

    /**
     * 权限类型（1:角色,其他类型暂未开发，其他值会报错）
     */
    // @NotNull(message = "permissionType 不能为空")
    @Deprecated
    private Integer permissionType;

    /**
     * 置灰数据，用户、角色权限列表
     */
    private List<String> beforeDeptIdLIst;

    /**
     * 高亮数据，本次新申请的数据
     */
    private List<String> currentApplicationDeptIdList;

    /**
     * 国家
     */
    private String country;

    /**
     * 系统
     */
    private String system;

    /**
     * 申请的角色国家
     */
    private String roleCountry;
}
