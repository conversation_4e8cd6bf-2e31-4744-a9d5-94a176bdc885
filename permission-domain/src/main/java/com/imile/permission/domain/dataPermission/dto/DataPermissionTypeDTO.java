package com.imile.permission.domain.dataPermission.dto;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/5
 */
@Data
public class DataPermissionTypeDTO {
    /**
     * 岗位数据权限
     */
    private PostDataPermissionDTO postDataPermissionDTO;
    /**
     * 角色数据权限
     */
    private List<RoleDataPermissionDTO> roleDataPermissionDTOList;
    /**
     * 直接授权数据权限
     */
    private List<DataPermissionDTO> dataPermissionList;


    public List<String> getTypeCode() {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataPermissionList)) {
            result.addAll(dataPermissionList.stream().map(DataPermissionDTO::getTypeCode).collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(roleDataPermissionDTOList)) {
            result.addAll(roleDataPermissionDTOList.stream()
                    .filter(roleDataPermissionDTO -> CollectionUtils.isNotEmpty(roleDataPermissionDTO.getDataPermissionList()))
                    .map(RoleDataPermissionDTO::getDataPermissionList)
                    .flatMap(List::stream)
                    .map(DataPermissionDTO::getTypeCode)
                    .distinct()
                    .collect(Collectors.toList()));
        }

        if (Objects.nonNull(postDataPermissionDTO)) {
            List<RoleDataPermissionDTO> postRoleDataList = postDataPermissionDTO.getRoleDataPermissionDTOList();
            if (CollectionUtils.isNotEmpty(postRoleDataList)) {
                result.addAll(postRoleDataList.stream()
                        .filter(roleDataPermissionDTO -> CollectionUtils.isNotEmpty(roleDataPermissionDTO.getDataPermissionList()))
                        .map(RoleDataPermissionDTO::getDataPermissionList)
                        .flatMap(List::stream)
                        .map(DataPermissionDTO::getTypeCode)
                        .distinct()
                        .collect(Collectors.toList()));
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }
}
