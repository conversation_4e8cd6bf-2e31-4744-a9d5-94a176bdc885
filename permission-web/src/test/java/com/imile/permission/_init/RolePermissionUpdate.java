package com.imile.permission._init;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.common.result.Result;
import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.dataPermission.vo.SystemDataPermissionVO;
import com.imile.permission.domain.role.dto.RoleBasicDTO;
import com.imile.permission.domain.role.param.RoleBasicParam;
import com.imile.permission.domain.role.param.RoleUpdateParam;
import com.imile.permission.domain.role.vo.SysRoleResultVO;
import com.imile.permission.domain.role.vo.SysRoleVO;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
public class RolePermissionUpdate {

    @Test
    public void test() {
        String roleId = "1770708308489723905";
        updateRole(roleId);
    }

    private void updateRole(String roleId) {
        HttpResponse<String> response = Unirest.get("https://uat-auth.imile.com/permission/sys/role/getByRoleIdSystemIsolation?id=" + roleId)
                .header("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("Authorization", "Bearer 98569a79-8fd7-45dd-b56f-3ce062421d67")
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36")
                .header("Accept", "application/json, text/plain, */*")
                .header("lang", "zh_CN")
                .header("Referer", "https://uat-auth.imile.com/")
                .header("Front-Sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("resource-code", "undefined")
                .header("timeZone", "+4")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("Cookie", "IMILE_ACCESS_TOKEN=98569a79-8fd7-45dd-b56f-3ce062421d67")
                .asString();
        System.out.println(response);

        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        Object resultObject = result.getResultObject();
        SysRoleVO sysRoleResultVO = JSON.parseObject(JSON.toJSONString(resultObject), SysRoleVO.class);

        RoleUpdateParam roleUpdateParam = new RoleUpdateParam();
        roleUpdateParam.setId(sysRoleResultVO.getId());
        RoleBasicParam roleBasicParam = new RoleBasicParam();
        RoleBasicDTO roleBasicDTO = sysRoleResultVO.getRoleBasicDTO();
        roleBasicParam.setRoleName(roleBasicDTO.getRoleName());
        roleBasicParam.setDescription(roleBasicDTO.getDescription());
        roleBasicParam.setBusinessType(roleBasicDTO.getBusinessType());
        roleBasicParam.setSystemCodeList(roleBasicDTO.getSystemCodeList());
        roleUpdateParam.setRoleBasicParam(roleBasicParam);

        List<SystemDataPermissionVO> baseDataPermissionDTO = sysRoleResultVO.getBaseDataPermissionDTO();
        List<SystemDataPermissionVO> mainDataPermissionDTO = sysRoleResultVO.getMainDataPermissionDTO();

        List<MenuPermissionDTO> menuPermissionDTOList = sysRoleResultVO.getMenuPermissionDTOList();

        List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(baseDataPermissionDTO)) {
            for (SystemDataPermissionVO systemDataPermissionVO : baseDataPermissionDTO) {
                dataPermissionDTOList.addAll(systemDataPermissionVO.getDataPermissionDTOList());
            }
        }

        if (CollectionUtils.isNotEmpty(mainDataPermissionDTO)) {
            for (SystemDataPermissionVO systemDataPermissionVO : mainDataPermissionDTO) {
                dataPermissionDTOList.addAll(systemDataPermissionVO.getDataPermissionDTOList());
            }
        }

        roleUpdateParam.setDataPermissionDTOList(dataPermissionDTOList);
        Map<String, MenuPermissionDTO> map = menuPermissionDTOList.stream().collect(Collectors.toMap(MenuPermissionDTO::getSystemPlatform, Function.identity()));
        MenuPermissionDTO menuPermissionDTO = map.get("iMile");
        if (Objects.isNull(menuPermissionDTO)) {
            MenuPermissionDTO imile = new MenuPermissionDTO();
            imile.setSystemPlatform("iMile");
            imile.setMenuId(-1130547762920960001L);
            imile.setMenuIdList(
                    Arrays.asList(
                            1233111867870691328L,
                            1233111828028985345L,
                            1233111828284837888L
                    )
            );
            imile.setPartiallyMenuIdList(
                    Arrays.asList(
                            1117900275064111105L,
                            1141499218851282944L,
                            1141498720077217793L,
                            -1130547762920960001L,
                            1233111827764744192L,
                            1233111780130316288L
                    )
            );
            menuPermissionDTOList.add(imile);
        } else {
            List<Long> menuIdList = menuPermissionDTO.getMenuIdList();
            Set<Long> menuIdSet = new HashSet<>(menuIdList);
            menuIdSet.addAll(
                    Arrays.asList(
                            1233111867870691328L,
                            1233111828028985345L,
                            1233111828284837888L
                    )
            );
            List<Long> partiallyMenuIdList = menuPermissionDTO.getPartiallyMenuIdList();
            Set<Long> partiallyMenuIdSet = new HashSet<>(partiallyMenuIdList);
            partiallyMenuIdSet.addAll(
                    Arrays.asList(
                            1117900275064111105L,
                            1141499218851282944L,
                            1141498720077217793L,
                            -1130547762920960001L,
                            1233111827764744192L,
                            1233111780130316288L
                    )
            );
            menuPermissionDTO.setMenuIdList(new ArrayList<>(menuIdSet));
            menuPermissionDTO.setPartiallyMenuIdList(new ArrayList<>(partiallyMenuIdSet));
        }
        roleUpdateParam.setMenuPermissionDTOList(menuPermissionDTOList);
        String jsonString = JSON.toJSONString(roleUpdateParam);
        System.out.println(roleUpdateParam);
        HttpResponse<String> updateResponse = Unirest.post("https://uat-auth.imile.com/permission/sys/role/update")
                .header("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("Authorization", "Bearer 98569a79-8fd7-45dd-b56f-3ce062421d67")
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36")
                .header("Content-Type", "application/json")
                .header("Accept", "application/json, text/plain, */*")
                .header("lang", "zh_CN")
                .header("Referer", "https://uat-auth.imile.com/")
                .header("Front-Sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("resource-code", "undefined")
                .header("timeZone", "+4")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("Cookie", "IMILE_ACCESS_TOKEN=98569a79-8fd7-45dd-b56f-3ce062421d67")
                .body(jsonString)
                .asString();

        System.out.println(updateResponse.getBody());
    }

    @Test
    public void test2() {
        RoleUpdateParam roleUpdateParam = new RoleUpdateParam();


    }
}
