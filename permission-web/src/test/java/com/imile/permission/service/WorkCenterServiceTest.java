package com.imile.permission.service;

import cn.hutool.core.net.NetUtil;
import com.imile.common.page.PaginationResult;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.workCenter.param.WorkCenterPageQueryParam;
import com.imile.permission.domain.workCenter.vo.WorkCenterVO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
class WorkCenterServiceTest extends ApplicationTest {


    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    WorkCenterService workCenterService;

    @Test
    public void testFindWorkCenterPage() {
        WorkCenterPageQueryParam workCenterPageQueryParam = new WorkCenterPageQueryParam();
        PaginationResult<WorkCenterVO> workCenterPage = workCenterService.findWorkCenterPage(workCenterPageQueryParam);
        System.out.println(workCenterPage);
    }


}