package com.imile.permission.domain.applicationApprove.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/4/7
 */
@Data
public class DirectAuthorizationApplicationDetailsQuery {

    /**
     * 资源ID
     */
    @NotBlank(message = "sourceId 不能为空")
    private String sourceId;

    /**
     * 资源类型
     */
    @NotBlank(message = "sourceType 不能为空")
    private String sourceType;

    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * 权限类型
     */
    private String permissionType;

    /**
     * 是否进行时区转换，默认为 true
     */
    private Boolean useTimezone = true;
}
