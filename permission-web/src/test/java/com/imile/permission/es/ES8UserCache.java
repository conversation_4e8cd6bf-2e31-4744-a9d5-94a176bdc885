package com.imile.permission.es;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.common.result.Result;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/23
 */
public class ES8UserCache {

    @Test
    public void test() {
        List<String> userCodeList = getUserCodeList("");
        for (String userCode : userCodeList) {
            exe(userCode);
        }

    }

    private void exe(String userCode) {
        HttpResponse<String> response = Unirest.get("https://uat-auth.imile.com/permission/other/pushEsWithCache?userCode=" + userCode)
                .header("authorization", "Bearer fb356de0-acbb-46ca-88af-f9a383a3ab19")
                .header("Cookie", "IMILE_ACCESS_TOKEN=fb356de0-acbb-46ca-88af-f9a383a3ab19")
                .asString();
        String updateResponseBody = response.getBody();
        Result r2 = JSONObject.parseObject(updateResponseBody, Result.class);
        if (!"success".equals(r2.getStatus())) {
            System.out.println(updateResponseBody + "=====" + userCode);
        } else {
            System.out.println(" success =====" + userCode);
        }
    }


    private List<String> getUserCodeList(String path) {
        List<String> userCodeList = Lists.newArrayList();
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(path), StandardCharsets.UTF_8));
            String line = bufferedReader.readLine();
            while (line != null) {
                if (StringUtils.isNotBlank(line)) {
                    userCodeList.add(line);
                }
                line = bufferedReader.readLine();
            }
            bufferedReader.close();
        } catch (IOException r) {
            throw new RuntimeException(r);
        }
        return userCodeList;
    }
}
