package com.imile.permission.domain.applicationApprove.param;

import com.imile.permission.domain.applicationApprove.dto.BeforeAndCurrentDataListDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
@Accessors(chain = true)
public class PermissionEditApplyRoleAndMainDataParam {

    /**
     * 审批流 ID
     */
    @NotNull(message = "approvalId 不能为空")
    private Long approvalId;

    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * 用户编号
     */
    @NotNull(message = "userId 不能为空")
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID")
    private Long deptId;

    /**
     * 角色编号
     */
    @NotNull(message = "roleId 不能为空")
    private Long roleId;

    /**
     * 有效时间（单位，天）
     */
    @NotNull(message = "effectiveTimeDay 不能为空")
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    @NotNull(message = "reason 不能为空")
    private String reason;

    /**
     * 业务类型
     */
    // @NotNull(message = "businessType 不能为空")
    private String businessType;

    /**
     * 主数据的高亮与置灰数据
     */
    private List<BeforeAndCurrentDataListDTO> beforeAndCurrentDataListDTOS;

    /**
     * 国家
     */
    private String country;

    /**
     * 系统
     */
    private String systemCode;

    /**
     * 申请的角色国家
     */
    private String roleCountry;


}
