package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
public class DimensionData {
    /**
     * 维度类型 ENUM（枚举）、DICT（字典）、MAIN_DATA（主数据）
     */
    private String dimensionType;

    /**
     * 维度类型code
     */
    private String dimensionTypeCode;
    /**
     * 维度类型名称
     */
    private String dimensionTypeName;
    /**
     * 维度类型英文名称
     */
    private String dimensionTypeNameEn;

    /**
     * 是否支持多选，0 不支持，1 支持
     */
    private String dimensionSupportMultiSelect;

    /**
     * 维度数据code
     */
    private List<DataCodeInfo> dimensionDataCodeInfoList;

    // /**
    //  * 主数据来源
    //  */
    // private String configId;

    /**
     * 接口URL
     */
    private String dimensionDataUrl;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;

}
