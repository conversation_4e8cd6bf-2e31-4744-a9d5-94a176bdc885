package com.imile.permission.integration.hermes.client;

import com.imile.hermes.resource.api.OrgSystemResourceApi;
import com.imile.hermes.resource.dto.ResResourceTreeForAuthApiDTO;
import com.imile.hermes.resource.dto.ResSystemResourceTreeDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConfigCenterConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/25
 */
public class OrgSystemResourceApiClientTest {


    @Test
    public void test() {
        RpcResult<List<Long>> parentAndChildMenuIDList = getOrgSystemResourceApi().getParentAndChildMenuIDList(10L, 22182913L);
        System.out.println(parentAndChildMenuIDList);
    }
    @Test
    public void testGetValidMenuId() {
        RpcResult<List<Long>> validMenuId = getOrgSystemResourceApi().getValidMenuId(10L, Arrays.asList(
                -1120481732894662657L,
                1164262059140194304L,
                1163938916274016256L,
                1163938883336146945L,
                -1163937441955188737L
        ));
        List<Long> result = validMenuId.getResult();
        System.out.println(result);
    }

    @Test
    public void getMenuChainMergedNameMapTest() {
        RpcResult<Map<Long, String>> zhCn = getOrgSystemResourceApi().getMenuChainMergedNameMap(10L, "en_US", Arrays.asList(

                433L,
                727511168930895344L,
                727511168930895345L,
                727511168930895346L,
                727511168930895347L,
                727511168930895348L,
                727511168930895349L


        ));
        Map<Long, String> result = zhCn.getResult();
        System.out.println(result);
    }

    @Test
    public void testConvertPartially() {
        RpcResult<List<Long>> listRpcResult = getOrgSystemResourceApi().convertPartially(10L, Arrays.asList(-1129038186413174784L));
        System.out.println(listRpcResult);
    }

    @Test
    public void getMenuTree() {
        HashMap<Long, List<Long>> object = new HashMap<>();
        List<Long> longs = Arrays.asList(-1129038186413174784L, 1129059803717578753L, 1160577423092494336L);
        object.put(-1129038186413174784L, longs);
        object.put(-722917713374361527L, Arrays.asList(1069770020176793601L, 862089782367490048L));


        RpcResult<List<ResSystemResourceTreeDTO>> rpc = getOrgSystemResourceApi().getMenuTree(10L,
                null,
                "zh_CN");
        System.out.println(rpc);
    }

    @Test
    public void getAuthTreeByResourceId() {
        //RpcResult<List<ResResourceTreeForAuthApiDTO>> treeByResourceId = getOrgSystemResourceApi().getAuthTreeByResourceId(10L, null, "AUTH", null, new String[]{"RESOURCE_COMPONENT"}, "zh_CN");
        RpcResult<List<ResResourceTreeForAuthApiDTO>> treeByResourceId = getOrgSystemResourceApi().getAuthTreeByResourceId(
                10L,
                null,
                null,
                Arrays.asList(
                        "HRMS",
                        "AUTH"
                ),
                //new String[]{"RESOURCE_COMPONENT"},
                // new String[]{"RESOURCE_DIR", "RESOURCE_MENU"},9
                null,
                "zh_CN",
                null

        );
        System.out.println(treeByResourceId);

    }

    public OrgSystemResourceApi getOrgSystemResourceApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<OrgSystemResourceApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("OrgSystemResourceApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        // Map<String, String> parameters = new HashMap<>();
        // parameters.put("include.spring.env", "false");
        // parameters.put("timeout", "1000000");
        // referenceConfig.setParameters(parameters);
        // referenceConfig.setUrl("dubbo://**********:18108");
        // referenceConfig.setUrl("dubbo://**********:18085");
        referenceConfig.setUrl("dubbo://localhost:18085");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(OrgSystemResourceApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        ConfigCenterConfig configCenterConfig = new ConfigCenterConfig();
        configCenterConfig.setTimeout(10000000L);
        ConfigManager.getInstance().setConfigCenter(
                configCenterConfig
        );
        return referenceConfig.get();
    }
}
