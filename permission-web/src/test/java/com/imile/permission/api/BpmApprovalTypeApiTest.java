package com.imile.permission.api;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.imile.bpm.api.BpmApprovalApi;
import com.imile.bpm.api.BpmApprovalTypeApi;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeApiQueryDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.permission.enums.ApprovalCustomFieldEnum;
import com.imile.permission.util.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@Slf4j
public class BpmApprovalTypeApiTest {

    @Test
    public void test() {
        BpmApprovalTypeApi bpmApprovalApi = getBpmApprovalApi();
        ApprovalTypeApiQueryDTO query = new ApprovalTypeApiQueryDTO();
        query.setBizSource("HRMS");

        RpcResult<List<ApprovalTypeApiDTO>> listRpcResult = bpmApprovalApi.selectPermissionApprovalTypeList(query);
        List<ApprovalTypeApiDTO> process = RpcResultProcessor.process(listRpcResult);
        System.out.println(process);
    }

    public BpmApprovalTypeApi getBpmApprovalApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<BpmApprovalTypeApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("BpmApprovalTypeApi"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        //referenceConfig.setUrl("dubbo://localhost:18108");


        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(BpmApprovalTypeApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }


}
