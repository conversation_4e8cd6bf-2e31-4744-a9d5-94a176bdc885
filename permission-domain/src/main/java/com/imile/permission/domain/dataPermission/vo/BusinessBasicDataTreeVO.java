package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class BusinessBasicDataTreeVO {
    /**
     * id
     */
    private String id;

    /**
     * 子菜单
     */
    private List<BusinessBasicDataTreeVO> childrenList;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 别名
     */
    private String alias;

    public static void removeDisable(BusinessBasicDataTreeVO businessBasicDataTreeVO) {
        List<BusinessBasicDataTreeVO> nestedList = businessBasicDataTreeVO.getChildrenList();
        if (CollectionUtils.isNotEmpty(nestedList)) {
            Iterator<BusinessBasicDataTreeVO> iterator = nestedList.iterator();
            while (iterator.hasNext()) {
                BusinessBasicDataTreeVO next = iterator.next();
                if (StringUtils.isNotBlank(next.getAlias())) {
                    next.setName(next.getName() + "(" + next.getAlias() + ")");
                }
                if (StringUtils.isNotBlank(next.getStatus()) && "DISABLED".equals(next.getStatus())) {
                    iterator.remove();
                }
                removeDisable(next);
            }
        }
    }
}
