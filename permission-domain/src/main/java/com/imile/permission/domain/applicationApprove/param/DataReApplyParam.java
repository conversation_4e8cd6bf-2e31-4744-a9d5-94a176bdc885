package com.imile.permission.domain.applicationApprove.param;

import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import com.imile.permission.domain.dataPermission.vo.MultiDynamicDataConfigValueDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
public class DataReApplyParam {

    /**
     * 审批流 ID
     */
    @NotNull(message = "approvalId 不能为空")
    private Long approvalId;


    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * 用户编号
     */
    @NotNull(message = "userId 不能为空")
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID")
    private Long deptId;

    /**
     * 有效时间（单位，天）
     */
    @NotNull(message = "effectiveTimeDay 不能为空")
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    @NotNull(message = "reason 不能为空")
    private String reason;


    /**
     * 系统编码
     */
    @NotNull(message = "systemCode 不能为空")
    private String systemCode;

    /**
     * 基础数据权限
     */
    private List<DataPermissionRuleDTO> baseDataPermissionDTO;

    /**
     * 动态数据权限
     */
    private List<DataPermissionRuleDTO> dynamicDataPermissionDTO;

    /**
     * 主数据权限
     */
    private List<DataPermissionRuleDTO> mainDataPermissionDTO;

    /**
     * 单数据维度
     */
    private List<DataPermissionRuleDTO> singleDynamicDataConfigValueDTO;

    /**
     * 多数据维度
     */
    private List<MultiDynamicDataConfigValueDTO> multiDynamicDataConfigValueDTO;

}
