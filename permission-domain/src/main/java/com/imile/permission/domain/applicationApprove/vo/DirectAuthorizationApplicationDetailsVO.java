package com.imile.permission.domain.applicationApprove.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DirectAuthorizationApplicationDetailsVO {


    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据编号
     */
    private String applicationCode;

    /**
     * 审批流 ID
     */
    private Long approvalId;

}
