<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.RuleDynamicDataPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.RuleDynamicDataPermissionDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="authorization_type" property="authorizationType" />
        <result column="user_code" property="userCode" />
        <result column="role_id" property="roleId" />
        <result column="type_code" property="typeCode" />
        <result column="data_code" property="dataCode" />
        <result column="single_system" property="singleSystem" />
        <result column="is_associated_main_data" property="isAssociatedMainData" />
        <result column="data_content" property="dataContent" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, authorization_type, user_code, role_id, type_code, data_code, single_system, is_associated_main_data, data_content
    </sql>

</mapper>
