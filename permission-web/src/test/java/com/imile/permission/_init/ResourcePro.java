package com.imile.permission._init;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.imile.common.result.Result;
import com.imile.idwork.IdWorkerUtil;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/1
 */
public class ResourcePro {

    @Test
    public void test() {
    }

    @Test
    public void testUpdateRoleResource() {
        String authorization = "Bearer 22bbf54a-c53c-4b83-9fda-e1928762f659";
        String roleId = "1176951691996831745";

        updateRoleResource(authorization, roleId);

    }

    private void updateRoleResource(String authorization, String roleId) {
        JSONArray roleResource = getRoleResource(authorization, roleId);
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(roleResource)) {
            if (roleResource.contains(cloverId)) {
                roleResource.addAll(cloverAddList);
                flag = true;
            }
            if (roleResource.contains(zpId)) {
                roleResource.addAll(zpAddList);
                flag = true;
            }
        }
        if (flag) {
            updateRole(authorization, roleId, roleResource.toJavaList(String.class));
        }
    }


    @Test
    public void testUpdateRole() {
        updateRole("Bearer 22bbf54a-c53c-4b83-9fda-e1928762f659", "1176951691996831745", Arrays.asList("-1019707597306994689", "1019743325789622273", "1019743359134212096", "1019743390994280449", "1052963607207485441"));
    }


    //@Test
    public void testRoleList() {
        String authorization = "Bearer 5073ab89-e744-4475-bce2-bef8c97dd369";
        for (int i = 10; i < 11; i++) {
            System.out.println(i);
            JSONObject o = roleList(authorization, i);
            JSONArray results = o.getJSONArray("results");
            if (CollectionUtils.isNotEmpty(results)) {
                for (int j = 0; j < results.size(); j++) {
                    Long roleId = results.getJSONObject(j).getLong("id");
                    System.out.println(roleId);
                    updateRoleResource(authorization, roleId.toString());
                }
            }
        }

    }

    public JSONObject roleList(String authorization, int currentPage) {
        HttpResponse<String> response = Unirest.post("https://uat-scs.imile.com/hermes/perm/role/queryRoles")
                .header("authority", "uat-scs.imile.com")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", authorization)
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.1194054492.1696657779; _ga_3F6R7164X1=GS1.1.1704712625.31.0.1704712625.0.0.0; _ga_0C4M1PWYZ7=GS1.1.1705650107.11.0.1705650107.0.0.0; _ga_K2SPJK2C73=GS1.1.1705650107.11.0.1705650107.60.0.0; _ga_T11SF3WXX2=GS1.1.1705650107.11.0.1705650107.60.0.0; LANG=zh_CN; timezone=+8; timezoneCountry=CHN; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; settleOrg={%22id%22:%22823321544150220803%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210127%22%2C%22downstreamOrgIds%22:%2211%2C80995%22%2C%22companyName%22:%22iMile%20Saudi%20Logistics%20Services%20Company%20LLC%22%2C%22companyShortName%22:%22%5Ct%5Cr%5CniMile%20KSA%22%2C%22registerCountry%22:%22KSA%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%22%2C%22dutyParagraph%22:%22310256003900003%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308141115025624608727040/iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210127%22%2C%22comprehensiveBaseCurrency%22:%22SAR%22%2C%22decimalPlaces%22:%2202%22}; currentOcCode=********; currentOcName=Test%20CHN%20Center; userCountry=CHN; userCountry=CHN; ACCESS_TOKEN=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; ACCESS_TOKEN_SAS=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659; cookieTimeZone=8; cookieTimeZone=8; countryName=CHN; countryName=CHN; countryCode=1000001; countryCode=1000001; IMILE_TIME_ZONE=+8; page_key=SCSRoleManagement; LANG=zh_CN; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10127\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://uat-scs.imile.com")
                .header("referer", "https://uat-scs.imile.com/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+8")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .body("{\n    \"showCount\": 100,\n    \"currentPage\": " + currentPage + ",\n    \"roleName\": \"\"\n}")
                .asString();
        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        JSONObject resultObject = (JSONObject) result.getResultObject();
        return resultObject;
    }

    @Test
    public void testGetRoleResource() {
        JSONArray roleResource = getRoleResource("Bearer 22bbf54a-c53c-4b83-9fda-e1928762f659", "1176951691996831745");
    }

    public JSONArray getRoleResource(String authorization, String roleId) {
        HttpResponse<String> response = Unirest.get("https://uat-scs.imile.com/hermes/perm/role/queryRoleResourceIds?roleId=" + roleId)
                .header("authority", "uat-scs.imile.com")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", authorization)
                .header("cookie", "_ga=GA1.1.1194054492.1696657779; _ga_3F6R7164X1=GS1.1.1704712625.31.0.1704712625.0.0.0; _ga_0C4M1PWYZ7=GS1.1.1705650107.11.0.1705650107.0.0.0; _ga_K2SPJK2C73=GS1.1.1705650107.11.0.1705650107.60.0.0; _ga_T11SF3WXX2=GS1.1.1705650107.11.0.1705650107.60.0.0; LANG=zh_CN; timezone=+8; timezoneCountry=CHN; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; settleOrg={%22id%22:%22823321544150220803%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210127%22%2C%22downstreamOrgIds%22:%2211%2C80995%22%2C%22companyName%22:%22iMile%20Saudi%20Logistics%20Services%20Company%20LLC%22%2C%22companyShortName%22:%22%5Ct%5Cr%5CniMile%20KSA%22%2C%22registerCountry%22:%22KSA%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%22%2C%22dutyParagraph%22:%22310256003900003%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308141115025624608727040/iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210127%22%2C%22comprehensiveBaseCurrency%22:%22SAR%22%2C%22decimalPlaces%22:%2202%22}; currentOcCode=********; currentOcName=Test%20CHN%20Center; userCountry=CHN; userCountry=CHN; ACCESS_TOKEN=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; ACCESS_TOKEN_SAS=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659; cookieTimeZone=8; cookieTimeZone=8; countryName=CHN; countryName=CHN; countryCode=1000001; countryCode=1000001; IMILE_TIME_ZONE=+8; page_key=SCSRoleManagement; LANG=zh_CN; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10127\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("referer", "https://uat-scs.imile.com/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+8")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .asString();

        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        JSONArray resultObject = (JSONArray) result.getResultObject();
        return resultObject;
    }

    public void updateRole(String authorization, String roleId, List<String> resourceIds) {

        RoleResourceDTO roleResourceDTO = new RoleResourceDTO();
        roleResourceDTO.setRoleId(roleId);
        roleResourceDTO.setResourceIds(new HashSet<>(resourceIds));

        HttpResponse<String> response = Unirest.post("https://uat-scs.imile.com/hermes/perm/role/updateResourceRelationByRoleId")
                .header("authority", "uat-scs.imile.com")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", authorization)
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.1194054492.1696657779; _ga_3F6R7164X1=GS1.1.1704712625.31.0.1704712625.0.0.0; _ga_0C4M1PWYZ7=GS1.1.1705650107.11.0.1705650107.0.0.0; _ga_K2SPJK2C73=GS1.1.1705650107.11.0.1705650107.60.0.0; _ga_T11SF3WXX2=GS1.1.1705650107.11.0.1705650107.60.0.0; LANG=zh_CN; timezone=+8; timezoneCountry=CHN; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; settleOrg={%22id%22:%22823321544150220803%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210127%22%2C%22downstreamOrgIds%22:%2211%2C80995%22%2C%22companyName%22:%22iMile%20Saudi%20Logistics%20Services%20Company%20LLC%22%2C%22companyShortName%22:%22%5Ct%5Cr%5CniMile%20KSA%22%2C%22registerCountry%22:%22KSA%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%22%2C%22dutyParagraph%22:%22310256003900003%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308141115025624608727040/iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210127%22%2C%22comprehensiveBaseCurrency%22:%22SAR%22%2C%22decimalPlaces%22:%2202%22}; currentOcCode=********; currentOcName=Test%20CHN%20Center; userCountry=CHN; userCountry=CHN; ACCESS_TOKEN=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; ACCESS_TOKEN_SAS=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659; cookieTimeZone=8; cookieTimeZone=8; countryName=CHN; countryName=CHN; countryCode=1000001; countryCode=1000001; IMILE_TIME_ZONE=+8; page_key=SCSRoleManagement; LANG=zh_CN; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10127\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://uat-scs.imile.com")
                .header("referer", "https://uat-scs.imile.com/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+8")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .body(JSON.toJSONString(roleResourceDTO))
                .asString();
        String body = response.getBody();

    }


    private Result getMenuTree(String authorization) {
        HttpResponse<String> response = Unirest.get("https://uat-scs.imile.com/hermes/res/orgResource/getTree?isRemoveAction=true")
                .header("authority", "uat-scs.imile.com")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer 22bbf54a-c53c-4b83-9fda-e1928762f659")
                .header("cookie", "_ga=GA1.1.1194054492.1696657779; _ga_3F6R7164X1=GS1.1.1704712625.31.0.1704712625.0.0.0; _ga_0C4M1PWYZ7=GS1.1.1705650107.11.0.1705650107.0.0.0; _ga_K2SPJK2C73=GS1.1.1705650107.11.0.1705650107.60.0.0; _ga_T11SF3WXX2=GS1.1.1705650107.11.0.1705650107.60.0.0; LANG=zh_CN; timezone=+8; timezoneCountry=CHN; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; settleOrg={%22id%22:%22823321544150220803%22%2C%22orgId%22:%2210%22%2C%22companyOrgId%22:%2210127%22%2C%22downstreamOrgIds%22:%2211%2C80995%22%2C%22companyName%22:%22iMile%20Saudi%20Logistics%20Services%20Company%20LLC%22%2C%22companyShortName%22:%22%5Ct%5Cr%5CniMile%20KSA%22%2C%22registerCountry%22:%22KSA%22%2C%22isSettleCenter%22:1%2C%22isSettleCenterDesc%22:null%2C%22businessArea%22:%22KSA%2CUAE%22%2C%22dutyParagraph%22:%22310256003900003%22%2C%22companyStatus%22:%22Working%22%2C%22companyStatusDesc%22:null%2C%22contractTemplate%22:%22[{%5C%22fileName%5C%22:%5C%22iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22templateType%5C%22:%5C%22normal%5C%22%2C%5C%22fileUrl%5C%22:%5C%22hermes/ent/mp3/default/2023/8/202308141115025624608727040/iMile%20KSA%20B2C%20client%20contract%20template%20-%20BL-V2.6.docx%5C%22%2C%5C%22langType%5C%22:%5C%22ar-en%5C%22%2C%5C%22fileType%5C%22:%5C%22docx%5C%22}]%22%2C%22contractTemplateList%22:null%2C%22accountSetCode%22:%2210127%22%2C%22comprehensiveBaseCurrency%22:%22SAR%22%2C%22decimalPlaces%22:%2202%22}; currentOcCode=********; currentOcName=Test%20CHN%20Center; userCountry=CHN; userCountry=CHN; ACCESS_TOKEN=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; ACCESS_TOKEN_SAS=Bearer%2022bbf54a-c53c-4b83-9fda-e1928762f659; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; IMILE_ACCESS_TOKEN=22bbf54a-c53c-4b83-9fda-e1928762f659; cookieTimeZone=8; cookieTimeZone=8; countryName=CHN; countryName=CHN; countryCode=1000001; countryCode=1000001; IMILE_TIME_ZONE=+8; page_key=SCSRoleManagement; LANG=zh_CN")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\",\"settleOrgId\":\"10127\",\"sysName\":\"TMS\"}")
                .header("lang", "zh_CN")
                .header("referer", "https://uat-scs.imile.com/")
                .header("request-token", "")
                .header("resource-code", "SCSRoleManagement")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("sys-name", "SCS")
                .header("timezone", "+8")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .asString();

        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        return result;
    }

    List<String> zpAddList = new ArrayList<>(Arrays.asList(
            "1176901083373785089",
            "1176904423864008704",
            "1154107067930521601",
            "1158801421219540992",
            "1158801338776162305",
            "-1019707597306994689"
    ));

    List<String> cloverAddList = new ArrayList<>(Arrays.asList(
            "1019743390994280449",
            "1052963607207485441",
            "1052971185798778881",
            "1052971209630945281",
            "1052963630754308096",
            "1170729980624392192",
            "1170729980846690304",
            "1170729981018656768",
            "1170729981194817536",
            "1170729981358395392",
            "1170729981551333376",
            "1170730038291873792",
            "1019743359134212096",
            "1019743325789622273",
            "-1019707597306994689",
            "1158801421219540992",
            "1158801338776162305"
    ));

    String cloverId = "-1019707597306994689";
    String zpId = "1154107067930521601";

}
