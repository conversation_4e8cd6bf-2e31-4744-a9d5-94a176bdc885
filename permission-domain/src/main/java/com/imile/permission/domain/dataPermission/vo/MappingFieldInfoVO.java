package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
public class MappingFieldInfoVO {

    /**
     * 来源字段
     */
    private String sourceField;

    /**
     * 字段类型
     */
    private String sourceFieldType;

    /**
     * 目标字段
     */
    private String targetField;

    /**
     * 目标字段类型（ String / Array）
     */
    private String targetFieldType;

    /**
     * 展示名称中文
     */
    private String displayNameCn;

    /**
     * 展示名称英文
     */
    private String displayNameEn;

    /**
     * 解释
     */
    private String description;

    /**
     * 是否支持搜索
     */
    private Integer isSupportSearch;


    /**
     * 是否展示
     */
    private Integer isDisplay;


    /**
     * 是否必须
     */
    private Integer isRequired;

    /**
     * 是否可编辑目标字段
     */
    private Integer isEditTargetField;

    /**
     * 是否可编辑展示
     */
    private Integer isEditDisplay;

    /**
     *  是否可编辑搜索
     */
    private Integer isEditSearch;

    /**
     * 序号
     */
    private Integer sortNo;

}
