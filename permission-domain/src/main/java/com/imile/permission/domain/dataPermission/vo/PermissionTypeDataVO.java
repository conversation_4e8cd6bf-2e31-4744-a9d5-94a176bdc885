package com.imile.permission.domain.dataPermission.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
public class PermissionTypeDataVO {

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 同权的系统来源
     */
    private String sourceSystem;

    @JsonIgnore
    private Date createDate;
    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 数据code
     */
    private List<LabelValueVO> dataCodeList;

    public void setDataCode(List<String> dataCode) {
        if (CollectionUtils.isNotEmpty(dataCode)) {
            dataCodeList = dataCode.stream()
                    .map(
                            e -> {
                                LabelValueVO labelValueVO = new LabelValueVO();
                                labelValueVO.setLabel(e);
                                labelValueVO.setValue(e);
                                return labelValueVO;
                            }
                    ).collect(Collectors.toList());
        }
    }

}
