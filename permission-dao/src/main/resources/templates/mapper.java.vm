package ${package.Mapper};

import ${package.Entity}.${entity};
import ${superMapperClassPackage};
import org.apache.ibatis.annotations.Mapper;


/**
 * <p>
 * $!{table.comment} Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Mapper
#if(${kotlin})
interface ${table.mapperName} : ${superMapperClass}<${entity}>
#else
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

}
#end
