package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class UserDefaultRoleVO {

    private String userCode;

    private Long roleId;

    private String roleName;

    /**
     * @see com.imile.permission.enums.RoleTypeEnum
     */
    private Integer roleType;

    private String roleTypeDesc;

    private List<String> systemCodeList;

    /**
     * 描述
     */
    public String description;

    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;

    private Integer isDisable;
}
