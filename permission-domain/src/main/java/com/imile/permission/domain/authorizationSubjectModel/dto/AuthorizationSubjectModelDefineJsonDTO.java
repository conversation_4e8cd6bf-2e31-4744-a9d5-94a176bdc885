package com.imile.permission.domain.authorizationSubjectModel.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/1/13
 */
@Data
public class AuthorizationSubjectModelDefineJsonDTO {
    private String method;
    private String url;
    private Map<String, String> header;
    private Map<String, String> param;

    private String mainIdMapping;
    private String nodeIdMapping;
    // private String parentNodeIdMapping;
    private String nameMapping;
    private String statusMapping;
    private String childrenMapping;

    // field -> (lang => nane)
    // name -> (zh_CN => 名字)
    // name -> (en_US => name)

    private Map<String, Map<String, String>> fieldDefineNameMapping;
}
