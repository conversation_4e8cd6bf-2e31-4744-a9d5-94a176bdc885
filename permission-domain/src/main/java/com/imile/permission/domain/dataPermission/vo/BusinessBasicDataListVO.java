package com.imile.permission.domain.dataPermission.vo;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class BusinessBasicDataListVO {

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;


    /**
     * 数据code
     */
    private List<BusinessBasicDataVO> dataCodeList;


    public void setIdNameVO(List<IdNameVO> result) {
        if (CollectionUtils.isNotEmpty(result)) {
            dataCodeList = result.stream()
                    .map(
                            e -> {
                                BusinessBasicDataVO vo = new BusinessBasicDataVO();
                                vo.setName(e.getName());
                                vo.setId(e.getId());
                                vo.setStatus(e.getStatus());
                                vo.setAlias(e.getAlias());
                                return vo;
                            }
                    ).collect(Collectors.toList());
        }
    }
}
