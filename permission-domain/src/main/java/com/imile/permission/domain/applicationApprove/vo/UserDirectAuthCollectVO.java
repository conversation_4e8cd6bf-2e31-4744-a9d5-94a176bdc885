package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/31
 */
@Data
public class UserDirectAuthCollectVO {


    private String userCode;

    /**
     * 资源ID
     */
    private String sourceId;

    /**
     * 资源类型
     */
    private String sourceType;

    /**
     * 重复次数
     */
    private Integer duplicateCount;

    /**
     * 重复的id，用逗号拼成字符串
     */
    private String duplicateIds;
}
