package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class BusinessBasicDataParam {

    /**
     * id
     */
    private Long id;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;

    /**
     * 数据接口 url
     */
    private String dataUrl;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 是否过滤禁用状态数据
     */
    private Integer isFilterDisabled;

    /**
     * 查询用户配置的规则
     */
    private String userCode;

    /**
     * 查询角色配置的规则
     */
    private Long roleId;

}
