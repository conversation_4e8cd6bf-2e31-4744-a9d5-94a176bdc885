package com.imile.permission.domain.applicationApprove.dto;


import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * 审批业务流程信息
 */
@Data
public class ApprovalMergeDetailProcessRecordDTO {

    private List<ApprovalDetailChildInfoDTO> detailChildInfoDTOS;

    /**
     * 审批人员
     */
    private List<ApprovalUserInfoDTO> approvalUserInfoDTOS = Lists.newArrayList();

    /**
     * 角色人员信息
     */
    private ApprovalCreateUserDTO approvalCreateUserDTO;

}
