package com.imile.permission.domain.dataPermission.dto;

import lombok.Data;

import java.util.List;

@Data
public class DimensionSystemTypeCodeDTO {

    /**
     * 数据维度
     * 0：单维度
     * 1：多维度
     */
    private Integer dimension;

    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 多维度数据Code
     */
    private List<String> dimensionTypeCodeList;


    /**
     * 多维度树型数据Code
     */
    private List<String> dimensionTreeTypeCodeList;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

}
