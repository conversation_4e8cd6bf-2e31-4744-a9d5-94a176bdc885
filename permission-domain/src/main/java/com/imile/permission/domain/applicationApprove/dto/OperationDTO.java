package com.imile.permission.domain.applicationApprove.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OperationDTO {
    private LocalDateTime expirationDate;
    private Long approvalId;
    private String operationType;
    private String operator;
    private LocalDateTime operationTime;
    private String remark;
    private String createUserCode;
    private String createUserName;
    private String lastUpdUserCode;
    private String lastUpdUserName;
}