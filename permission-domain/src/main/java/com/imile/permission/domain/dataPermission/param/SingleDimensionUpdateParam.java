package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
public class SingleDimensionUpdateParam {

    /**
     * 使用场景解释
     */
    private String useCaseDescription;
    /**
     * 动态函数部分
     */
    private List<DynamicDataNestedParam> dataList;
    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;

    /**
     * 映射部分
     */
    private List<MappingFieldInfoParam> fieldInfos;

}
