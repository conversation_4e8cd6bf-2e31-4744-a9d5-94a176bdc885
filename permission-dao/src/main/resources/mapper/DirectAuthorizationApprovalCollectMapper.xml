<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.DirectAuthorizationApprovalCollectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.DirectAuthorizationApprovalCollectDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="user_id" property="userId" />
        <result column="source_type" property="sourceType" />
        <result column="source_id" property="sourceId" />
        <result column="application_date" property="applicationDate" />
        <result column="expiration_date" property="expirationDate" />
        <result column="is_execute" property="isExecute" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_code, user_id, source_type, source_id, application_date, expiration_date, is_execute
    </sql>
    <select id="getApplicationRecordList"
            resultType="com.imile.permission.domain.applicationApprove.vo.DirectAuthorizationApplicationRecordVO">
        select
        user_code,
        source_type,
        source_id,
        application_date,
        expiration_date
        from direct_authorization_approval_collect
        where direct_authorization_approval_collect.is_delete = 0
        <if test="query.userCode!=null and query.userCode!=''">
            AND direct_authorization_approval_collect.user_code = #{query.userCode}
        </if>
        <if test="query.isExpired!=null and !query.isExpired">
            and direct_authorization_approval_collect.expiration_date &gt;= #{query.expirationDate}
        </if>
        <if test="query.isExpired!=null and query.isExpired">
            and direct_authorization_approval_collect.expiration_date &lt;= #{query.expirationDate}
        </if>
        <if test="query.sourceTypeList!=null and query.sourceTypeList.size()>0">
            and source_type in
            <foreach collection="query.sourceTypeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        order by source_type desc, expiration_date,id
    </select>
    <select id="getApplicationRecordEffectiveCount" resultType="java.lang.Integer">
        select
        count(*)
        from direct_authorization_approval_collect
        where direct_authorization_approval_collect.is_delete = 0
        AND direct_authorization_approval_collect.user_code = #{userCode}
        and direct_authorization_approval_collect.expiration_date &gt; #{now}
        and source_type in
        <foreach collection="sourceTypeList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>
    <select id="getApplicationRecordLossCount" resultType="java.lang.Integer">
        select
        count(*)
        from direct_authorization_approval_collect
        where direct_authorization_approval_collect.is_delete = 0
        AND direct_authorization_approval_collect.user_code = #{userCode}
        and direct_authorization_approval_collect.expiration_date &lt;= #{now}
        and source_type in
        <foreach collection="sourceTypeList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>

    <select id="getUserDirectAuthCollect" resultType="com.imile.permission.domain.applicationApprove.vo.UserDirectAuthCollectVO">
        SELECT user_code, source_type, source_id, COUNT(*) as duplicateCount,GROUP_CONCAT(id) as duplicateIds
        FROM direct_authorization_approval_collect
        GROUP BY user_code, source_type, source_id
        HAVING COUNT(*) > 1
    </select>

    <insert id="batchUpsert">
        <foreach collection="collectDOList" item="item" separator=";">
            INSERT INTO direct_authorization_approval_collect
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id, </if>
                <if test="item.isDelete != null">is_delete, </if>
                <if test="item.recordVersion != null">record_version, </if>
                <if test="item.createDate != null">create_date, </if>
                <if test="item.createUserCode != null">create_user_code, </if>
                <if test="item.createUserName != null">create_user_name, </if>
                <if test="item.lastUpdDate != null">last_upd_date, </if>
                <if test="item.lastUpdUserCode != null">last_upd_user_code, </if>
                <if test="item.lastUpdUserName != null">last_upd_user_name, </if>
                <if test="item.userCode != null">user_code, </if>
                <if test="item.userId != null">user_id, </if>
                <if test="item.sourceType != null">source_type, </if>
                <if test="item.sourceId != null">source_id, </if>
                <if test="item.applicationDate != null">application_date, </if>
                <if test="item.expirationDate != null">expiration_date, </if>
                <if test="item.isExecute != null">is_execute, </if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id}, </if>
                <if test="item.isDelete != null">#{item.isDelete}, </if>
                <if test="item.recordVersion != null">#{item.recordVersion}, </if>
                <if test="item.createDate != null">#{item.createDate}, </if>
                <if test="item.createUserCode != null">#{item.createUserCode}, </if>
                <if test="item.createUserName != null">#{item.createUserName}, </if>
                <if test="item.lastUpdDate != null">#{item.lastUpdDate}, </if>
                <if test="item.lastUpdUserCode != null">#{item.lastUpdUserCode}, </if>
                <if test="item.lastUpdUserName != null">#{item.lastUpdUserName}, </if>
                <if test="item.userCode != null">#{item.userCode}, </if>
                <if test="item.userId != null">#{item.userId}, </if>
                <if test="item.sourceType != null">#{item.sourceType}, </if>
                <if test="item.sourceId != null">#{item.sourceId}, </if>
                <if test="item.applicationDate != null">#{item.applicationDate}, </if>
                <if test="item.expirationDate != null">#{item.expirationDate}, </if>
                <if test="item.isExecute != null">#{item.isExecute}, </if>
            </trim>

            ON DUPLICATE KEY UPDATE
            <trim prefix="" suffixOverrides=",">
                <if test="item.applicationDate != null">application_date=VALUES(application_date), </if>
                <if test="item.expirationDate != null">expiration_date=VALUES(expiration_date), </if>
                <if test="item.lastUpdDate != null">last_upd_date = VALUES(last_upd_date), </if>
                <if test="item.lastUpdUserCode != null">last_upd_user_code = VALUES(last_upd_user_code), </if>
                <if test="item.lastUpdUserName != null">last_upd_user_name = VALUES(last_upd_user_name), </if>
            </trim>
        </foreach>
    </insert>

    <select id="getApprovalCollectDTOList" resultType="com.imile.permission.domain.permission.dto.ApprovalCollectDTO">
        select id, user_code, user_id, source_type, source_type as permissionType, source_id as relateId, application_date, expiration_date
        from direct_authorization_approval_collect
        where direct_authorization_approval_collect.is_delete = 0
        <if test="query.startExpireTime != null">
            and direct_authorization_approval_collect.expiration_date &gt;= #{query.startExpireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.endExpireTime != null">
            and direct_authorization_approval_collect.expiration_date &lt;= #{query.endExpireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.relateIds != null and query.relateIds.size()>0">
            and id in
            <foreach collection="query.relateIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
