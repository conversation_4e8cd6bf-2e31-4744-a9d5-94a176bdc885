package com.imile.permission.domain.dataPermission.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/9
 */
@Data
public class DimensionConfigDTO {
    /**
     * 数据字典类型
     */
    private String typeCode;

    /**
     * 数据字典名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String typeName;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 字典数据
     */
    private List<DataPermissionRuleDTO> dimensionConfigList;

}