```sql
ALTER TABLE `sys_role`
    ADD COLUMN `risk_level` tinyint NULL DEFAULT 1 COMMENT '风险等级' ;
```

```sql
-- 示例表结构
CREATE TABLE `role_application_approve`
(
    -- 通用字段
    `id`                  bigint(20) unsigned NOT NULL COMMENT '主键ID',
    `is_delete`           tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除（0:未删除 1:已删除）',
    `record_version`      bigint(20) unsigned NOT NULL DEFAULT '1' COMMENT '版本号',
    `create_date`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user_code`    varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人编码',
    `create_user_name`    varchar(128) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `last_upd_date`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `last_upd_user_code`  varchar(64)  NOT NULL DEFAULT '' COMMENT '最后更新人编码',
    `last_upd_user_name`  varchar(128) NOT NULL DEFAULT '' COMMENT '最后更新人名称',
    -- 业务字段
    `user_code`           varchar(64)  NOT NULL DEFAULT '' COMMENT '用户编码',
    `user_id`             bigint(20) NOT NULL COMMENT '用户编号',
    `role_id`             bigint(20) NOT NULL COMMENT '角色编号',
    `application_date`    datetime              DEFAULT NULL COMMENT '申请日期',
    `expiration_date`     datetime              DEFAULT NULL COMMENT '失效日期',
    `effective_time_day`  int(11) DEFAULT NULL COMMENT '有效时间（单位，天）',
    `reason`              text                  DEFAULT NULL COMMENT '申请原因',
    `approve_status`      tinyint(4) DEFAULT NULL COMMENT '审批状态',
    `approve_create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审批创建时间',
    `approved_time`       datetime              DEFAULT NULL COMMENT '审批通过时间',
    `approval_date`       datetime              DEFAULT NULL COMMENT '审批时间',
    `risk_level`          tinyint(4) DEFAULT '1' COMMENT '风险等级',
    `business_type`       varchar(255)          DEFAULT NULL COMMENT '业务类型',
    -- 主键索引
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色申请审批表';
```