package com.imile.permission.domain.applicationApprove.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
@Data
public class ApprovalQuery {

    private String userCode;

    /**
     * @see com.imile.permission.enums.ApprovalInfoStatusEnum
     */
    private Integer approvalStatus;

    /**
     * @see com.imile.permission.enums.PermissionApplyTypeEnum
     */
    private Integer permissionType;

    /**
     * 系统编码
     */
    private String systemCode;
}
