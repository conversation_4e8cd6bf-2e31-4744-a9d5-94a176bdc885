package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
public class ApplicationRecordVOListVO {

    /**
     * 生效列表
     */
    private List<ApplicationRecordVO> effectiveList = new ArrayList<>();
    /**
     * 过期列表
     */
    private List<ApplicationRecordVO> loseEffectiveList = new ArrayList<>();
}
