package com.imile.permission.api;

import com.imile.permission.api.dto.DataApiDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConfigCenterConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.context.ConfigManager;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
class UserManagerApi2Test {

    @Test
    void getAllData() {
        RpcResult<List<DataApiDTO>> allData = getUserManagerApi()
                .getAllData("21032080");
        System.out.println(allData);
    }

    @Test
    void getDataBySystem() {
        RpcResult<List<DataApiDTO>> allData = getUserManagerApi()
                .getDataBySystem("21032080", "CRM");
        System.out.println(allData);
    }

    @Test
    void getDataByTypeCode() {
        RpcResult<List<DataApiDTO>> allData = getUserManagerApi()
                .getDataByTypeCode("21032080", "CRM_multi_001");
        System.out.println(allData);
    }


    @Test
    void getUserCodeByTCDC() {
        RpcResult<List<String>> userCodeByTCDC = getUserManagerApi().getUserCodeByTCDC("CBMS_SALARY_MANAGE", "y");
        System.out.println(userCodeByTCDC);
    }

    @Test
    void getUserCodeByMenu() {
        RpcResult<List<String>> userCodes = getUserManagerApi().selectUserCodeByMenuId("iMile", 1140387023761317888L);
        System.out.println(userCodes);
    }

    public UserManagerApi2 getUserManagerApi() {
        // EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<UserManagerApi2> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("userManagerApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        referenceConfig.setUrl("dubbo://***********:18108");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(UserManagerApi2.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        // referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        ConfigCenterConfig configCenterConfig = new ConfigCenterConfig();
        configCenterConfig.setTimeout(10000000L);
        ConfigManager.getInstance().setConfigCenter(
                configCenterConfig
        );
        return referenceConfig.get();
    }
}