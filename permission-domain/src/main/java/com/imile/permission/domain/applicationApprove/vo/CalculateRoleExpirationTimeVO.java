package com.imile.permission.domain.applicationApprove.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CalculateRoleExpirationTimeVO {


    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;


}
