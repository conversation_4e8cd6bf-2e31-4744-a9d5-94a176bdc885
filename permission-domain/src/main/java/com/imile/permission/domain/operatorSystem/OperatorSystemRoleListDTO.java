package com.imile.permission.domain.operatorSystem;

import lombok.Data;

import java.util.Date;

@Data
public class OperatorSystemRoleListDTO {

    private Long id;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 角色所属机构级别
     */
    private String adminOrg;



    /**
     * xmile 系统角色类型字段
     */
    private Integer xmileRoleType;

    /**
     * 角色所属站点编码
     */
    private String ownStationCode;

    /**
     * 备注
     */
    private String description;

    /**
     * 国家编码
     */
    private String countryCode;


    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

}
