package com.imile.permission.domain.dataPermission.vo;

import com.imile.permission.domain.dataPermission.param.DimensionData;
import com.imile.permission.domain.dataPermission.param.DimensionDataExt;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/9
 */

@Data
public class MultiDynamicDataConfigValueVO {
    private Long id;
    private Integer dimension;
    private List<DimensionDataExt> multiDimensionConfig;
}