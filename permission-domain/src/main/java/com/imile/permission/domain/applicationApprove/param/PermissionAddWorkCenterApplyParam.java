package com.imile.permission.domain.applicationApprove.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
@Accessors(chain = true)
public class PermissionAddWorkCenterApplyParam {

    /**
     * 用户编码
     */
    @NotNull(message = "userCode 不能为空")
    private String userCode;

    /**
     * 用户编号
     */
    @NotNull(message = "userId 不能为空")
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID")
    private Long deptId;

    /**
     * 有效时间（单位，天）
     */
    @NotNull(message = "effectiveTimeDay 不能为空")
    private Integer effectiveTimeDay;

    /**
     * 申请原因
     */
    @NotNull(message = "reason 不能为空")
    private String reason;


    /**
     * 工作中心ID
     */
    @NotNull(message = "workCenterId 不能为空")
    private Long workCenterId;

    /**
     * 国家
     */
    private String country;

    /**
     * 系统
     */
    private String system;


}
