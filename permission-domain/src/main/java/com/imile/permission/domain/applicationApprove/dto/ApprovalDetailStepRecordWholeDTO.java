package com.imile.permission.domain.applicationApprove.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 审批业务整体流程信息
 */
@Data
public class ApprovalDetailStepRecordWholeDTO {

    /**
     * 审批流步骤名称
     */
    private String stepName;

    /**
     * 审核记录状态
     */
    private Integer recordStatus;

    /**
     * 审核状态名称
     */
    private String recordStatusName;

    /**
     * 审核记录状态变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordStatusUpdateDate;

    private String approvalRecordType;

    /**
     * 审批人员
     */
    private List<ApprovalUserInfoDTO> approvalUserInfoDTOS = Lists.newArrayList();

    /**
     * 角色人员信息
     */
    private ApprovalCreateUserDTO approvalCreateUserDTO;

    /**
     * 组流程
     */
    private List<ApprovalMergeDetailProcessRecordDTO> mergeProcessRecordDTOS;




    /**
     * 节点流程
     */
    //private List<ApprovalMergeDetailUserProcessRecordDTO> userProcessRecordDTOS;
}

