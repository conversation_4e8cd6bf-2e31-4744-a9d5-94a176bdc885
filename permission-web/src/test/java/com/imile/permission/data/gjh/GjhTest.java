package com.imile.permission.data.gjh;

import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/8/27
 */
public class GjhTest {

    @Test
    public void test() {

        for (String xxx : Arrays.asList(
                "2103490701",
                "2103538901",
                "2103450401",
                "2103599301",
                "210086",
                "2103623802",
                "2103450301",
                "2103477901",
                "21032403",
                "2103650102",
                "D2101809601",
                "2103636001",
                "2103575401",
                "2103647901",
                "2103543101",
                "2103378601",
                "2103356201",
                "2103491401",
                "2103647202",
                "2103429601"
        )) {
            HttpResponse<String> response = Unirest.get("http://localhost:80/other/pushEsWithCache?userCode=" + xxx)
                    .header("accept", "application/json, text/plain, */*")
                    .header("accept-language", "zh-CN,zh;q=0.9")
                    .header("authorization", "Bearer db70c18d-7e6b-42f5-b32a-e8abb7ee851f")
                    .header("content-type", "application/json")
                    .header("cookie", "_ga=GA1.1.*********.**********; userCountry=UAE; UserInfo={%22password%22:%22$2a$10$XcYcAnCOG0s8MuAlsgcsqOXLuo41zc7RRR1tHrsBaArWaWqX4ekku%22%2C%22username%22:%22test-neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1134922065288314900%2C%22mobile%22:%22%22%2C%22userCode%22:%************%22%2C%22userName%22:%22test-neil%22%2C%22orgId%22:10%2C%22ocId%22:1175902985352519700%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22UAE%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%22S2101321701%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:null%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; countryName=BRA; cookieTimeZone=-3; countryCode=C1579303177974423554; _ga_3F6R7164X1=GS1.1.**********.292.0.**********.0.0.0; UserInfo={%22password%22:%22$2a$10$XcYcAnCOG0s8MuAlsgcsqOXLuo41zc7RRR1tHrsBaArWaWqX4ekku%22%2C%22username%22:%22test-neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1134922065288314900%2C%22mobile%22:%22%22%2C%22userCode%22:%************%22%2C%22userName%22:%22test-neil%22%2C%22orgId%22:10%2C%22ocId%22:1175902985352519700%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22UAE%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%22S2101321701%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:null%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; TIMEZONE=+4; ACCESS_TOKEN_OMS=Bearer%20d57d565c-c598-4de8-98ef-a7c2c7444174; lang=zh_CN; page_key_oms=OMS_home; userInfo_OMS=%<EMAIL>%22%2C%22deleteStatus%22%3Anull%2C%22deleteRequestDate%22%3Anull%2C%22wechatId%22%3Anull%2C%22userMfaInfoDTO%22%3A%7B%22checkMfa%22%3Afalse%2C%22checkSuccess%22%3Afalse%2C%22mobile%22%3Anull%2C%22email%22%3Anull%2C%22wechatId%22%3Anull%2C%22totpSecret%22%3Anull%7D%7D; LANG=zh_CN; currentOcName=UAE%E7%BD%91%E7%82%B9; _ga_JNSLCF76NH=GS1.1.1724660576.2.0.1724660576.0.0.0; page_key=SEADSControlTower; TIMEZONE_COUNTRY=UAE; userCode=2103451701; password=Cs1cscs1@; IMILE_ACCESS_TOKEN=db70c18d-7e6b-42f5-b32a-e8abb7ee851f; IMILE_TIME_ZONE=8; IMILE_LANG=zh_CN; IMILE_ORG_ID=10; IMILE_USER_CODE=2103451701; IMILE_USER_TYPE=OWN; JSESSIONID=CA0BFEB82D9590CE988E96FECD869EEB; ACCESS_TOKEN=Bearer%20db70c18d-7e6b-42f5-b32a-e8abb7ee851f; currentOcCode=99990002; currentOcCode=99990002; IMILE_ACCESS_TOKEN=db70c18d-7e6b-42f5-b32a-e8abb7ee851f")
                    .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"2103451701\",\"moduleId\":10007,\"client\":\"pc\"}")
                    .header("lang", "zh_CN")
                    .header("origin", "https://test-auth.52imile.cn")
                    .header("priority", "u=1, i")
                    .header("referer", "https://test-auth.52imile.cn/")
                    .header("resource-code", "SEADSControlTower")
                    .header("sec-ch-ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"")
                    .header("sec-ch-ua-mobile", "?0")
                    .header("sec-ch-ua-platform", "\"macOS\"")
                    .header("sec-fetch-dest", "empty")
                    .header("sec-fetch-mode", "cors")
                    .header("sec-fetch-site", "same-origin")
                    .header("timezone", "-3")
                    .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36")
                    .asString();
            String body = response.getBody();
            System.out.println(body);
        }


    }
}
