package com.imile.permission.dao;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.entity.SysDataPermissionDO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/20
 */
public class SysDataPermissionDAOTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
        System.setProperty("spring.datasource.driver-class-name", "com.mysql.cj.jdbc.Driver");
        System.setProperty("spring.datasource.url", "**********************************************************************************************************************************");
        System.setProperty("spring.datasource.username", "root");
        System.setProperty("spring.datasource.password", "123456");
    }

    @Autowired
    SysDataPermissionDAO sysDataPermissionDAO;

    @Test
    public void test() {
        sysDataPermissionDAO.selectType("tong");
        sysDataPermissionDAO.selectType(null);
        System.out.println();
    }


}
