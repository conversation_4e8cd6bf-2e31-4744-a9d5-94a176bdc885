package com.imile.permission.domain.dataPermission.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/25
 */
@Data
public class CommonDataPermissionConfigVO implements Serializable {

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型显示名称
     */
    private String showTypeName;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 数据类型英文名称
     */
    private String typeNameEn;

    /**
     * 同权的系统来源
     */
    private String sourceSystem;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 配置类型： 1-基础数据 2-主数据 3-动态数据 4-单维度动态数据 5-多维度动态数据
     */
    private Integer configType;


    @JsonIgnore
    private Date createDate;

    /**
     * 基础数据配置内容
     */
    private List<LabelValueVO> basicDataLabelValue;

    /**
     * 主数据配置内容
     */
    private MainDataConfigValueVO mainDataConfigValue;

    /**
     * 动态权限数据配置内容
     */
    private DynamicDataConfigValueVO dynamicDataConfigValue;

    /**
     * 单维度动态数据配置内容
     */
    private SingleDynamicDataConfigValueVO singleDynamicDataConfigValue;

    /**
     * 多维度动态数据配置内容
     */
    private MultiDynamicDataConfigValueVO multiDynamicDataConfigValue;

    /**
     * false 启用，true 禁用，默认为 false
     */
    private Boolean isModuleAdminDisable = Boolean.FALSE;


}
