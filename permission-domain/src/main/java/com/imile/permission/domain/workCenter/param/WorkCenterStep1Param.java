package com.imile.permission.domain.workCenter.param;

import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterStep1Param {

    /**
     * 工作中心ID
     * 不传工作中心ID，进行新增逻辑
     * 传工作中心ID，进行修改逻辑
     */
    private Long workCenterId;

    /**
     * 工作中心名称
     */
    @Size(max = 30, message = "不能超过 30 字符")
    private String workCenterName;

    /**
     * 描述
     */
    @Size(max = 500, message = "不能超过 500 字符")
    private String description;

    /**
     * 授权类型（1:公共访问 2:授权角色）
     */
    @Deprecated
    private Integer authorizationType = 1;

    /**
     * 授权类型为 2:授权角色 使用
     * 角色ID 集合
     */
    @Deprecated
    private List<Long> roleIdList;

    /**
     * 系统code
     */
    private String systemCode;

}
