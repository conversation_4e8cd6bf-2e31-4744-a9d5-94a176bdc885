package com.imile.permission.domain.dataPermission.vo;

import com.imile.permission.domain.dataPermission.param.MultiDimensionVO;
import com.imile.permission.domain.dataPermission.param.SingleDimensionVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/9
 */
@Data
public class CustomDynamicDataConfigNestedVO {

    private Long customDynamicId;

    /**
     * 数据维度
     * 0：单维度
     * 1：多维度
     */
    private Integer dimension;

    /**
     * 单维度数据配置
     */
    private SingleDimensionVO singleDimensionVO;

    /**
     * 多维度数据配置
     */
    private MultiDimensionVO multiDimensionVO;

}
