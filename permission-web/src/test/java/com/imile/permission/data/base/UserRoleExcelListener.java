package com.imile.permission.data.base;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;

import java.util.List;

public class UserRoleExcelListener implements ReadListener<UserRoleExcel> {

    public List<UserRoleExcel> cachedDataList = ListUtils.newArrayListWithExpectedSize(10000);

    public List<UserRoleExcel> getCachedDataList() {
        return cachedDataList;
    }

    @Override
    public void invoke(UserRoleExcel userRoleExcel, AnalysisContext analysisContext) {
        cachedDataList.add(userRoleExcel);

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
