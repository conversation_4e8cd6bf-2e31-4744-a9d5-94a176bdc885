package com.imile.permission.api;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.imile.bpm.api.BpmApprovalApi;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.permission.enums.ApprovalCustomFieldEnum;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@Slf4j
public class BpmApprovalApiTest {

    @Test
    public void test() {
        BpmApprovalApi bpmApprovalApi = getBpmApprovalApi();
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        approvalDataBuild(initInfoApiDTO);
        RpcResult<ApprovalInfoCreateResultDTO> longRpcResult = bpmApprovalApi.addApprovalInfoV2(initInfoApiDTO);
        System.out.println(longRpcResult);
    }

    public BpmApprovalApi getBpmApprovalApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<BpmApprovalApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("BpmApprovalApiTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        //referenceConfig.setUrl("dubbo://localhost:18108");


        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(BpmApprovalApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }

    public void approvalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO) {
        initInfoApiDTO.setBizId(String.valueOf(IdWorker.getId()));
        initInfoApiDTO.setApprovalType("roleApprove008");
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());

        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.PERMISSION.getCode());
        initInfoApiDTO.setApplyUserCode("2103451701");
        initInfoApiDTO.setApplyDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        initInfoApiDTO.setAppointApprovalCode("TEST_RA" + IdWorker.getId());
        // 处理参数
        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();

        // 构建数据
        customFieldBuild(fieldApiDTOList, ApprovalCustomFieldEnum.USER_ID.getCode(), "1", null);
        customFieldBuild(fieldApiDTOList, ApprovalCustomFieldEnum.USER_CODE.getCode(), "1", null);
        customFieldBuild(fieldApiDTOList, ApprovalCustomFieldEnum.ROLE_ID.getCode(), "1", null);
        customFieldBuild(fieldApiDTOList, ApprovalCustomFieldEnum.BUSINESS_TYPE.getCode(), "1", null);
        customFieldBuild(fieldApiDTOList, ApprovalCustomFieldEnum.DEPT_ID.getCode(), "1", null);
        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("approvalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void customFieldBuild(List<ApprovalTypeFieldApiDTO> fieldApiDTOList, String fieldType, String fieldValue, Map<String, String> fieldValueMap) {
        ApprovalTypeFieldApiDTO fieldApiDTO = new ApprovalTypeFieldApiDTO();
        fieldApiDTO.setFieldType(fieldType);
        fieldApiDTO.setFieldValue(fieldValue);
        fieldApiDTO.setFieldValueMap(fieldValueMap);
        fieldApiDTOList.add(fieldApiDTO);
    }

}
