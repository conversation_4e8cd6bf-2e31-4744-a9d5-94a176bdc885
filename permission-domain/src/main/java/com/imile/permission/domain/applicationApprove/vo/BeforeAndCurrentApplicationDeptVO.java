package com.imile.permission.domain.applicationApprove.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Data
public class BeforeAndCurrentApplicationDeptVO {

    /**
     * 置灰数据，用户、角色权限列表
     */
    private List<String> beforeDeptIdLIst;

    /**
     * 高亮数据，本次新申请的数据
     */
    private List<String> currentApplicationDeptIdList;

    /**
     * 高亮数据，本次新申请的数据
     */
    private List<String> roleNewDeptIdList;

}
