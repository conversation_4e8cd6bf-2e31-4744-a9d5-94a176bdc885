package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
public class MappingFieldInfoParam {

    /**
     * 来源字段
     */
    @NotBlank(message = "来源字段不可为空")
    private String sourceField;

    /**
     * 字段类型
     */
    @NotBlank(message = "来源字段类型不可为空")
    private String sourceFieldType;

    /**
     * 目标字段
     */
    @NotBlank(message = "目标字段不可为空")
    private String targetField;

    /**
     * 目标字段类型 String / Array 默认String
     */
    private String targetFieldType = "String";


    /**
     * 展示名称中文
     */
    private String displayNameCn;

    /**
     * 展示名称中文
     */
    private String displayNameEn;

    /**
     * 解释
     */
    private String description;

    /**
     * 是否支持搜索 默认否
     */
    private Integer isSupportSearch = 0;


    /**
     * 是否展示（不传默认是）
     */
    private Integer isDisplay = 1;


    /**
     * 是否必须（不传默认否）
     */
    private Integer isRequired = 0;

    /**
     * 是否可编辑目标字段
     */
    private Integer isEditTargetField = 1;

    /**
     * 是否可编辑展示
     */
    private Integer isEditDisplay = 1;

    /**
     *  是否可编辑搜索
     */
    private Integer isEditSearch = 1;

    /**
     * 序号
     */
    private Integer sortNo;
}
