package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Data
public class DynamicQuery {
    private String userCode;

    private List<NestedQueryParam> query;


    @Data
    public static class NestedQueryParam {
        /**
         * 关联类型（主数据、函数数据）
         */
        private String relationType;

        /**
         * 关联数据类型
         */
        private String relationTypeCode;

        /**
         * 规则编码
         */
        private String ruleCode;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 规则数据类型
         */
        private String ruleType;

        /**
         * 值类型
         */
        private String valueType;

        /**
         * 值列表
         */
        private String valueContent;

        /**
         * 操作符
         */
        private String op;

        /**
         * 操作符描述
         */
        private String opDesc;
    }


}
