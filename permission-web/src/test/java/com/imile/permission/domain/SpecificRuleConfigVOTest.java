package com.imile.permission.domain;

import com.imile.permission.domain.dataPermission.vo.SpecificRuleConfigVO;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/11/11
 */
public class SpecificRuleConfigVOTest {

    @Test
    public void test() {
        SpecificRuleConfigVO vo = new SpecificRuleConfigVO();
        vo.setRelationType("number");
        vo.setRelationTypeCode("number");
        vo.setRuleCode("number");
        vo.setRuleName("number");
        vo.setRuleType("number");
        vo.setValueType("number");
        vo.setValueContent("1,a");
        vo.setOp("eq");
        vo.setOpDesc("number");
        vo.check();
    }
}
