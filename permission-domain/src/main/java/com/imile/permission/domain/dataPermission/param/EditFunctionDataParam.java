package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class EditFunctionDataParam {
    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 动态函数部分
     */
    private List<DynamicDataNestedParam> dataList;

    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;
}
