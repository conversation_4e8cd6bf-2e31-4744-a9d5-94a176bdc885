package com.imile.permission.data.pms.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;

import java.util.List;

public class DemoDataListener implements ReadListener<DemoData> {

    public List<DemoData> getCachedDataList() {
        return cachedDataList;
    }

    /**
     * 缓存的数据
     */
    public List<DemoData> cachedDataList = ListUtils.newArrayListWithExpectedSize(10000);

    @Override
    public void invoke(DemoData demoData, AnalysisContext analysisContext) {
        cachedDataList.add(demoData);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
