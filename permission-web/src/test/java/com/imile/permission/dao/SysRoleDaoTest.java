package com.imile.permission.dao;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.entity.SysRoleDO;
import com.imile.permission.domain.role.query.SysRoleQuery;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

public class SysRoleDaoTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }


    @Autowired
    SysRoleDao sysRoleDAO;

    @Test
    public void test() {
        SysRoleQuery query = new SysRoleQuery();
        query.setNonOwnedSystemCodeList(Arrays.asList("HRMS","iMile"));
        List<SysRoleDO> select = sysRoleDAO.select(query);
        System.out.println(select);
    }


}
