package com.imile.permission.domain.workCenter.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Data
public class BaseNodeDTO {
    private String shape;
    private JSONObject attrs;
    private Integer zIndex;
    private JSONObject source;
    private JSONObject target;

    private String id;
    /**
     * type
     */
    private String type;

    /**
     * sourceNodeId
     */
    private String sourceNodeId;
    /**
     * targetNodeId
     */
    private String targetNodeId;

    /**
     * startPoint
     */
    private JSONObject startPoint;


    /**
     * endPoint
     */
    private JSONObject endPoint;

    /**
     * properties
     */
    private JSONObject properties;

    /**
     * pointsList
     */
    private JSONArray pointsList;

}
