package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@Data
public class DataPermissionTypeDataAddParam {
    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 数据详情
     */
    private List<SimpleDataItemParam> dataItemList;
}
