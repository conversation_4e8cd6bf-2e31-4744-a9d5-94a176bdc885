package com.imile.permission.domain.workCenter.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Data
public class ApprovalStepNodeDTO {
    private JSONObject position;
    private JSONObject size;
    private JSONObject attrs;
    private Boolean visible;
    private String shape;
    private JSONObject ports;
    private Integer zIndex;


    /**
     * 节点id
     */
    private String id;

    /**
     * type
     */
    private String type;

    /**
     * x坐标
     */
    private Long x;


    /**
     * y坐标
     */
    private Long y;

    /**
     * properties
     */
    private JSONObject properties;

    /**
     * text
     */
    private JSONObject text;

    /**
     * 节点操作属性
     */
    private List<LangDTO> lang;
}
