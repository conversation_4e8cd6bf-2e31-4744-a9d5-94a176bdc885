package com.imile.permission.future;

import org.junit.jupiter.api.Test;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @since 2024/9/9
 */
public class CompletableFutureTest {


    @Test
    public void test() {
        ExecutorService executor = Executors.newFixedThreadPool(5);

        CompletableFuture<String> f1 = CompletableFuture.supplyAsync(
                () -> getSleepStr(),
                executor
        );

        CompletableFuture<String> f2 = CompletableFuture.supplyAsync(
                () -> getStr(),
                executor
        );


        String s = null;
        try {
            s = f1.get(200, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            System.out.println("Task did not complete within the specified timeout.");
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("An error occurred while trying to get the result: " + e.getMessage());
            Thread.currentThread().interrupt();
        }

        if (Objects.isNull(s)) {
            try {
                s = f2.get();
            } catch (InterruptedException | ExecutionException e) {
                System.err.println("An error occurred while trying to get the result: " + e.getMessage());
                Thread.currentThread().interrupt();
            }
        }
        System.out.println(s);

    }

    public void test1() {
        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> getSleepStr());
        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> getStr());

        CompletableFuture<Object> anyResult = CompletableFuture.anyOf(future1, future2);

        try {
            Object o = anyResult.get(200, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            // 超时或其他异常，取消所有未完成的任务
            future1.cancel(true);
            future2.cancel(true);
        }
    }

    public static String getSleepStr() {
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return "getSleepStr";
    }


    public static String getStr() {
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return "Str";
    }

}
