<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.RoleAuthorityApprovalDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.RoleAuthorityApprovalDetailDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_code" property="userCode" />
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="application_date" property="applicationDate" />
        <result column="expiration_date" property="expirationDate" />
        <result column="is_system_operation_type" property="isSystemOperationType" />
        <result column="operation_type" property="operationType" />
        <result column="operator" property="operator" />
        <result column="operation_time" property="operationTime" />
        <result column="remark" property="remark" />
        <result column="application_code" property="applicationCode" />
        <result column="approval_id" property="approvalId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_code, user_id, role_id, application_date, expiration_date, is_system_operation_type, operation_type, operator, operation_time, remark, application_code, approval_id
    </sql>

</mapper>
