package com.imile.permission.domain.workCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.domain.workCenter.dto.TemplateModelConfigDTO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/6/22
 */
@Data
public class WorkCenterWithNodeVO {

    private Long workCenterId;

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 修改人名称
     */
    private String lastUpdUserName;

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 节点数
     */
    private Integer nodeCount;

    /**
     * 审批流程配置
     */
    private TemplateModelConfigDTO templateModelConfigDTO;
}
