package com.imile.permission.service;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;

public class HrmsOrganizationUpdateServiceTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    private HrmsOrganizationUpdateService service;

    @Test
    public void testAddOrganization() {
        service.addOrganization(900101L, null, 900100L);
    }

    @Test
    public void testMoveOrganization() {
        service.moveOrganization(900101L, Arrays.asList(900100L), Collections.emptyList(), Arrays.asList(900111L), 900100L);
    }
}
