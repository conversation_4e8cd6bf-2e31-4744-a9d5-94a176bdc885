package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class ExtensionTagParam {

    /**
     * 数据数据扩展标记
     */
    @Size(max = 20, message = "标签编码长度不能超过20")
    @NotBlank( message = "扩展标签编码不能为空")
    private String extensionTagCode;
    /**
     * 数据数据扩展名称
     */
    @Size(max = 50, message = "标签名长度不能超过50")
    @NotBlank( message = "扩展标签名称不能为空")
    private String extensionTagName;
}
