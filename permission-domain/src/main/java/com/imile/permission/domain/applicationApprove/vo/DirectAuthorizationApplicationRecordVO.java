package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <p>
 * 角色申请审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
public class DirectAuthorizationApplicationRecordVO {

    /**
     * 系统code
     */
    private String systemCode;


    /**
     * 权限类型
     */
    private String permissionType;

    /**
     * 权限名称
     */
    private String permissionName;


    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;


    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 资源ID
     */
    private String sourceId;

    /**
     * 资源类型
     */
    private String sourceType;


    /**
     * 状态
     */
    private String status;

    /**
     * 敏感等级： 1.敏感，0.不敏感
     */
    private Integer sensitiveLevel;


}
