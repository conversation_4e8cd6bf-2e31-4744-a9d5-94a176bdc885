package com.imile.permission.other;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.imile.common.result.Result;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
public class PermissionTest {

    @Test
    public void test() {
        String hrmsDevHost = "https://dev-ehr.52imile.cn/hrms";
        String localhost = "http://localhost:80";
        String authorization = "9868065a-619c-49d5-8147-91db3b7cfce8";
        String hrmsJson = JSON.toJSONString(accountList(hrmsDevHost, authorization));
        String localJson = JSON.toJSONString(accountList(localhost, authorization));
        boolean equals = hrmsJson.equals(localJson);
        System.out.println(equals);


    }

    public Object accountList(String host, String authorization) {
        HttpResponse<String> response = Unirest.post(host + "/account/list")
                .header("authority", "dev-ehr.52imile.cn")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer " + authorization)
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=UAE; _ga_3F6R7164X1=GS1.1.**********.158.0.**********.0.0.0; ACCESS_TOKEN=Bearer%20********-3915-40d1-9a73-f1997f671316; TIMEZONE=+4; TIMEZONE_COUNTRY=UAE; currentOcCode=S2102353; IMILE_ACCESS_TOKEN=********-3915-40d1-9a73-f1997f671316; LANG=zh_CN; currentOcName=Dubai%20Return%20Center; cookieTimeZone=3; countryCode=C119; countryName=KSA; timezone=+4; timezoneCountry=UAE; page_key=SCSUserManagement; UserInfo={}")
                .header("front-sec", "{\"orgId\":0,\"moduleId\":10016,\"client\":\"pc\",\"entId\":0,\"userCode\":0}")
                .header("lang", "zh_CN")
                .header("origin", "https://dev-ehr.52imile.cn")
                .header("referer", "https://dev-ehr.52imile.cn/")
                .header("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"macOS\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("timezone", "+8")
                .header("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .body("{\"showCount\":20,\"currentPage\":1,\"userCodeOrName\":\"\",\"status\":\"\",\"administratorAttributes\":\"\"}")
                .asString();

        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        Object resultObject = result.getResultObject();
        return resultObject;
    }

    @Test
    public void esCacheReload() {



    }
}
