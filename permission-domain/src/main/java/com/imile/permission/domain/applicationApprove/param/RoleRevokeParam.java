package com.imile.permission.domain.applicationApprove.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@Data
@Accessors(chain = true)
public class RoleRevokeParam {

    /**
     * 用户编号
     */
    @NotBlank(message = "userCode不能为空")
    private String userCode;
    /**
     * 角色ID
     */
    @NotNull(message = "roleId不能为空")
    private Long roleId;
}
