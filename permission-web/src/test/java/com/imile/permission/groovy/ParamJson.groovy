package com.imile.permission.groovy

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
class ParamJson {
    String dataPermissionDTOList = """
[
    {
        "typeCode": "fmsFeeZoneType",
        "typeName": "报价区域类型",
        "dataCodeList": [
            "zipCode",
            "ds",
            "adminRegion"
        ]
    }
]
"""
    String menuIdList = """
[
        {
            "menuIdList": [
                "-1129038186413174784",
                "1129039225547796481",
                "1138561370166661120"
            ]
        }
]
"""

    String menuPermissionDTOList = """
[
        {
            "menuId": "-1129038186413174784",
            "systemPlatform": "iMile",
            "menuIdList": [
                "-1129038186413174784",
                "1129039225547796481",
                "1138561370166661120",
                "1138568063361486848",
                "1140387023761317888",
                "1150803333855051777",
                "1129068298588397569",
                "1141745277628329984",
                "1129059972605423617",
                "1129068325918482432",
                "1138578509212557313",
                "1138579949523968000",
                "1141378612453310465",
                "1138585489654292480",
                "1141382528410714113",
                "1140387100697436161",
                "1151221414641147904",
                "1157064682641829888",
                "1151221549303472128",
                "1150883031318532097",
                "1129059675736780800",
                "1129059103491108865",
                "1129058653211602944",
                "1129060050946633728",
                "1129058438329020417",
                "1129060169276338177",
                "1160577375449395200",
                "1131237719570128897",
                "1158426748745031681",
                "1129143539637301249",
                "1138578884283998208",
                "1138579756770533376",
                "1138580049323237376",
                "1158514292761567233",
                "1138585608432787457",
                "1138585739353792513",
                "1117900275064111105",
                "1140387216841908224",
                "1140387250568306688",
                "1140387278162632704",
                "1151221479715774465",
                "1151221507616284672",
                "1157064862468419584",
                "1157064889697841153",
                "1151221576503533569",
                "1150883432067502081",
                "1158848141097959424",
                "1150883466737618945",
                "1129059803717578753",
                "1129059761736790017",
                "1129059829764206593",
                "1129059705004634113",
                "1129059888660623360",
                "1129059853982117889",
                "1146163998560096257",
                "1129059171606605824",
                "1129059197258969089",
                "1129059217685229568",
                "1129059247322181632",
                "1129059454407553025",
                "1129059487165067265",
                "1129059563266519040",
                "1129059527002566657",
                "1129059273100374016",
                "1129058720161083392",
                "1129058779116220417",
                "1129058750167134209",
                "1129058688330510336",
                "1129058814600032257",
                "1129060084991799296",
                "1129060108597342209",
                "1129060128230879233",
                "1129058508139016193",
                "1129060197982154752",
                "1129060231368814592",
                "1129060270199681024",
                "1129060296036593664",
                "1160577423092494336",
                "1129059996684922881",
                "1158426782945386496",
                "1129143599171252224",
                "1129143660777189376",
                "1129143716678873088",
                "1129143807691075585",
                "1158122503286034433",
                "1140396521079185408",
                "1140397830679306241",
                "1140397902431264768",
                "1140397964783788032",
                "1140398085072232448",
                "1140398160645201921",
                "1140398256468271105",
                "1140398464065347584",
                "1140398547246784512",
                "1140398587545657344",
                "1158516704477978625",
                "1158516738548310016",
                "1140413263310495745",
                "1140413366142246913",
                "1117903946401316864",
                "1117903979095916544",
                "1117904015217262593",
                "1117903298171633665",
                "1117904058397622273",
                "1117904098323202049",
                "1121079941522522113",
                "1119988085275365377",
                "1158119071267426305",
                "1150884071040356352",
                "1150884096382341120",
                "1129143599448076289",
                "1140296177435279361",
                "1129143599724900352",
                "1129143599980752897",
                "1129143661100150785",
                "1140296231051067393",
                "1129143661376974848",
                "1129143661649604609",
                "1129143661930622976",
                "1129143662203252737",
                "1129143662484271104",
                "1129143756906442752",
                "1129143848359047169",
                "1129143913832132609",
                "1128749897064132609",
                "1140295566073528321",
                "1128749946389147648",
                "1140295609279053825",
                "1128749982250446848",
                "1128750027993526272",
                "1128750087112241152",
                "1128750166057431069",
                "1128750216875618304",
                "1117904142342422529",
                "1117904183585013761",
                "1117904231072923649",
                "1117904282650279937",
                "1117904329915891713",
                "1121551534010929152",
                "1121079983465562113",
                "1119988187666714625",
                "1129143757208432641",
                "1140296293504253953",
                "1129143757493645312",
                "1129143848673619968",
                "1140296361154183169",
                "1129143848954638337",
                "1129143849244045313",
                "1129143849520869376",
                "1129143914218008576",
                "1140296431073230848",
                "1129143914561941505",
                "1129143914901680128",
                "1128750273129623552",
                "1140295663159083009",
                "1128750324132360192",
                "1128750398614810625",
                "1140295705769017345",
                "1128750428922851329",
                "1128750497659105281",
                "1128750608237735936",
                "1140295774211670016",
                "1128750647299289089",
                "1128750703465213952",
                "1128750749069881344",
                "1159129203090919424",
                "1128750827247513600",
                "1140295849688170497",
                "1128750862127345664",
                "1128750933799612417",
                "1140295913466757120",
                "1128750988547862529",
                "1128751023058595841",
                "1128751061545529344",
                "1128751106864984064",
                "1140295976691695616",
                "1128751149005156353",
                "1128751186196049921",
                "1128751267087396864",
                "1140296040986181632",
                "1128751353104183297",
                "1128751441251676161",
                "1140296114550079489",
                "1128751661083537409"
            ]
        }
    ]
    """
    String roleAddApiDTO = """
{"adminOrg":"AC","adminRole":0,"description":"","isDisable":0,"menuPermissionDTOList":[{"menuId":-1129038186413174784,"systemPlatform":"iMile"}],"orgId":10,"roleName":"骑手APP角色21","userCode":"2103541602","userName":"老大"}
"""

    String roleUpdateApiDTO = """
{"roleId":1743241692145864706,"adminOrg":"HQ","adminRole":1,"description":"管理员","isDisable":0,"menuPermissionDTOList":[{"menuId":-1130547762920960001,"menuIdList":[1129059803717578753,1129059761736790017,1129059829764206593],"partiallyMenuIdList":[-1130547762920960001,1129039225547796481,1129068298588397569,1129059675736780800],"systemPlatform":"iMile"}],"roleName":"rpc-update-role","orgId":10}
"""

    String roleUpdateBasicInfoApiDTO = """
{"roleId":1743241692145864706,"adminOrg":"HQ","adminRole":1,"description":"管理员","isDisable":0,"menuPermissionDTOList":[{"menuId":-1130547762920960001,"menuIdList":[1129059803717578753,1129059761736790017,1129059829764206593],"partiallyMenuIdList":[-1130547762920960001,1129039225547796481,1129068298588397569,1129059675736780800],"systemPlatform":"iMile"}],"roleName":"rpc-update-role","orgId":10}
"""
    String dataPermissionApiDTOList = """
[
    {
        "typeCode": "fmsFeeZoneType",
        "typeName": "报价区域类型",
        "dataCodeList": [
            "zipCode",
            "ds",
            "adminRegion"
        ]
    }
]
"""

    String roleUpdatePermissionApiDTO = """
{"roleId":1743241692145864706,"adminOrg":"HQ","adminRole":1,"description":"管理员","isDisable":0,"menuPermissionDTOList":[{"menuId":-1130547762920960001,"menuIdList":[1129059803717578753,1129059761736790017,1129059829764206593],"partiallyMenuIdList":[-1130547762920960001,1129039225547796481,1129068298588397569,1129059675736780800],"systemPlatform":"iMile"}],"roleName":"rpc-update-role","orgId":10}
"""
}
