package com.imile.permission.domain.dataPermission.vo;

import com.imile.permission.domain.dataPermission.param.DynamicDataNestedParam;
import com.imile.permission.domain.dataPermission.param.MappingFieldInfoParam;
import com.imile.permission.domain.dataPermission.param.PreviewMultiDimensionDynamicDataParam;
import com.imile.permission.domain.dataPermission.param.RuleDataNestedParam;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class PreviewMultiDimensionDynamicDataVO {

}
