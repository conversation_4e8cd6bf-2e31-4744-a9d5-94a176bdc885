package com.imile.permission.integration.hrms.client;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.organization.api.BizOrganizationInfoApi;
import com.imile.hrms.api.organization.dto.EntPostApiDTO;
import com.imile.hrms.api.organization.dto.OrgUserInfoApiDTO;
import com.imile.hrms.api.organization.dto.OrgUserInfoDetailApiDTO;
import com.imile.hrms.api.organization.dto.UserInfoApiDTO;
import com.imile.hrms.api.organization.query.PostApiQuery;
import com.imile.hrms.api.organization.query.SelectUserInfoApiQuery;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Slf4j
class BizOrganizationInfoApiClientTest {

    @Test
    public void testUserInfoDetailByUserCodes() {

        BizOrganizationInfoApi hrmsPostIntegration = getBizOrganizationInfoApi();
        RpcResult<List<OrgUserInfoApiDTO>> d21021058011 = hrmsPostIntegration.getUserInfoByUserCodeList(Arrays.asList("D2102105801"));
        // RpcResult<List<OrgUserInfoDetailApiDTO>> d2102105801 = hrmsPostIntegration.userInfoDetailByUserCodes(Arrays.asList("D2102105801"));
        // System.out.println(d2102105801);
    }

    @Test
    public void selectUserInfoPage2() {
        int currentPage = 2;
        //int pageSize = 100;
        int pageSize = 50;
        BizOrganizationInfoApi hrmsPostIntegration = getBizOrganizationInfoApi();
        PaginationResult<UserInfoApiDTO> paginationResult = null;
        SelectUserInfoApiQuery selectUserInfoApiQuery = new SelectUserInfoApiQuery();

        log.info("当前分页: {}", currentPage);
        selectUserInfoApiQuery.setShowCount(pageSize);
        selectUserInfoApiQuery.setCurrentPage(currentPage++);
        exeQuery(hrmsPostIntegration, selectUserInfoApiQuery);
        paginationResult = hrmsPostIntegration.selectUserInfoPage(selectUserInfoApiQuery).getResult();
        List<UserInfoApiDTO> results = paginationResult.getResults();

        System.out.println(1);

    }

    private void exeQuery(BizOrganizationInfoApi hrmsPostIntegration, SelectUserInfoApiQuery selectUserInfoApiQuery) {
        PaginationResult<UserInfoApiDTO> paginationResult;
        paginationResult = hrmsPostIntegration.selectUserInfoPage(selectUserInfoApiQuery).getResult();
    }

    @Test
    public void selectUserInfoPage() {
        int currentPage = 1;
        int pageSize = 100;
        BizOrganizationInfoApi hrmsPostIntegration = getBizOrganizationInfoApi();
        PaginationResult<UserInfoApiDTO> paginationResult = null;
        do {
            SelectUserInfoApiQuery selectUserInfoApiQuery = new SelectUserInfoApiQuery();

            log.info("当前分页: {}", currentPage);
            selectUserInfoApiQuery.setShowCount(pageSize);
            selectUserInfoApiQuery.setCurrentPage(currentPage++);
            paginationResult = hrmsPostIntegration.selectUserInfoPage(selectUserInfoApiQuery).getResult();
            List<UserInfoApiDTO> results = paginationResult.getResults();

        } while (paginationResult.getPagination().getTotalPage() >= currentPage);
        System.out.println(1);

    }

    @Test
    public void getPostPage() {
        PostApiQuery postApiQuery = new PostApiQuery();
        RpcResult<PaginationResult<EntPostApiDTO>> postPage = getBizOrganizationInfoApi().getPostPage(postApiQuery);
        PaginationResult<EntPostApiDTO> result = postPage.getResult();
        System.out.println(result);

    }


    public BizOrganizationInfoApi getBizOrganizationInfoApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<BizOrganizationInfoApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("RoleManagerApiTest"));

        // 3.设置服务注册中心
        // referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        referenceConfig.setUrl("dubbo://localhost:18108");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(BizOrganizationInfoApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }

}