package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/30
 */
@Data
public class DynamicDataAddParam {

    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 数据类型名称
     */
    private String typeName;

    /**
     * 归属系统
     */
    private String singleSystem;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;



    /**
     * 数据内容（若不传，则只新增类型不新增数据）
     */
    private List<DynamicDataNestedParam> dataList;

    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;
}
