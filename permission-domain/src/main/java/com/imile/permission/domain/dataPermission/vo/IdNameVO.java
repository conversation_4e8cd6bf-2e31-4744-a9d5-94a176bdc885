package com.imile.permission.domain.dataPermission.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/11/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdNameVO {
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 别名
     */
    private String alias;
    /**
     * 状态
     */
    private String status;
}
