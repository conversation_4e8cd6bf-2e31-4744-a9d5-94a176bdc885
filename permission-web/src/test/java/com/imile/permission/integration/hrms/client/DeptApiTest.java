package com.imile.permission.integration.hrms.client;

import com.imile.hrms.api.enums.DeptDynamicFieldEnum;
import com.imile.hrms.api.organization.api.BizOrganizationInfoApi;
import com.imile.hrms.api.organization.api.DeptApi;
import com.imile.hrms.api.organization.dto.DeptDTO;
import com.imile.hrms.api.organization.dto.DeptDynamicInfoDTO;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import com.imile.permission.util.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/1/17
 */
public class DeptApiTest {

    @Test
    public void test2() {
        DeptApi deptApi = getDeptApi();
        RpcResult<DeptDynamicInfoDTO> deptDynamicInfo = deptApi.getDeptDynamicInfo("1032652",
                Arrays.asList(
                        DeptDynamicFieldEnum.BIZ_COUNTRY
                ));
        DeptDynamicInfoDTO process = RpcResultProcessor.process(deptDynamicInfo);
        Map<String, String> dynamicFieldMap = process.getDynamicFieldMap();
        System.out.println(process);
    }

    @Test
    public void test() {
        DeptApi deptApi = getDeptApi();
        RpcResult<List<DeptDTO>> listRpcResult = deptApi.listDeptByCondition(new DeptConditionParam());
        List<DeptDTO> result = listRpcResult.getResult();
        System.out.println(result);
    }

    public DeptApi getDeptApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<DeptApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("RoleManagerApiTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        // referenceConfig.setUrl("dubbo://************:18108");

        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(DeptApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }
}
