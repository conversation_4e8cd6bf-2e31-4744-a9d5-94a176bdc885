package com.imile.permission.service;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
public class SysDataPermissionServiceTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    private SysDataPermissionService sysDataPermissionService;

    @Test
    public void test() {
        //PermissionBaseTypeVO permissionBaseDataTypeVO = sysDataPermissionService.typeList();
        //System.out.println(permissionBaseDataTypeVO);
    }
}
