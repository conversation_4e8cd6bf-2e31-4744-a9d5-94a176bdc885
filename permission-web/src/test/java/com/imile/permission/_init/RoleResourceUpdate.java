package com.imile.permission._init;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.common.result.Result;
import com.imile.permission.domain.dataPermission.dto.MenuPermissionDTO;
import com.imile.permission.domain.role.param.RoleUpdateParam;
import com.imile.permission.domain.role.vo.SysRoleResultVO;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class RoleResourceUpdate {
    @Test
    public void test03() {
        updateRoleResource("1770708308489723905");

    }

    @Test
    public void test() {
        for (String s : Arrays.asList(
                // "765111111111793701",
                // "768567608831381505",
                // "769971938935443457",
                // "769973075843485696",
                // "780950330354569216",
                // "942117665772806144",
                // "1039434005797277697",
                // "1177266694599622656",
                // "1196160119390486528",
                // "1196164277145321472",
                // "1203909736442101761",
                // "1204774973797830656",
                // "1205027881848418304",
                "1219920197759291392",
                "1219920342093537281",
                "1222193224190398465")) {
            System.out.println(s);
            updateRoleResource(s);
        }

    }

    private static void updateRoleResource(String roleId) {
        HttpResponse<String> response = Unirest.get("https://uat-auth.imile.com/permission/sys/role/find?id=" + roleId)
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer 1fabd8f3-11c5-4d0b-af72-0d3094b3f6c2")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=CHN; cookieTimeZone=4; countryCode=2000001; countryName=UAE; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; ACCESS_TOKEN=Bearer%20b23ee65e-6824-496b-aea6-45fe56137715; _ga_8Y83MQKMZD=GS1.1.**********.1.1.**********.0.0.0; _ga_MTPDGX60Z3=GS1.1.1715853438.1.1.**********.0.0.0; _ga_3F6R7164X1=GS1.1.1715861119.6.0.1715861119.0.0.0; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; currentOcCode=88880008; currentOcCode=88880008; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("lang", "zh_CN")
                .header("priority", "u=1, i")
                .header("referer", "https://uat-auth.imile.com/")
                .header("resource-code", "undefined")
                .header("sec-ch-ua", "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"Windows\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36")
                .asString();
        String body = response.getBody();
        Result result = JSONObject.parseObject(body, Result.class);
        Object resultObject = result.getResultObject();
        SysRoleResultVO sysRoleResultVO = JSON.parseObject(JSON.toJSONString(resultObject), SysRoleResultVO.class);
        System.out.println(sysRoleResultVO);

        RoleUpdateParam param = new RoleUpdateParam();
        param.setId(sysRoleResultVO.getId());
        List<MenuPermissionDTO> menuPermissionDTOList = sysRoleResultVO.getMenuPermissionDTOList();

        if (CollectionUtils.isNotEmpty(menuPermissionDTOList)) {
            menuPermissionDTOList.forEach(
                    e -> {
                        e.setMenuIdList(e.getMenuIdList().stream().distinct().collect(Collectors.toList()));
                        e.setPartiallyMenuIdList(e.getPartiallyMenuIdList().stream().distinct().collect(Collectors.toList()));
                    }
            );
        }
        param.setMenuPermissionDTOList(menuPermissionDTOList);
        param.setDataPermissionDTOList(sysRoleResultVO.getDataPermissionDTOList());
        ;
        String jsonString = JSON.toJSONString(param);
        HttpResponse<String> updateResponse = Unirest.post("https://uat-auth.imile.com/permission/sys/role/update/permission")
                .header("accept", "application/json, text/plain, */*")
                .header("accept-language", "zh-CN,zh;q=0.9")
                .header("authorization", "Bearer 1fabd8f3-11c5-4d0b-af72-0d3094b3f6c2")
                .header("content-type", "application/json")
                .header("cookie", "_ga=GA1.1.*********.**********; userCountry=CHN; cookieTimeZone=4; countryCode=2000001; countryName=UAE; UserInfo={%22password%22:%22$2a$10$aEXx7evY14pIZLUfcUaASuLrF7nDqaEVADp51VcYd/d9FyeiFMEY2%22%2C%22username%22:%22test-Neil%22%2C%22authorities%22:[]%2C%22accountNonExpired%22:true%2C%22accountNonLocked%22:true%2C%22credentialsNonExpired%22:true%2C%22enabled%22:true%2C%22id%22:1140652086028107800%2C%22mobile%22:%22%22%2C%22userCode%22:%***********%22%2C%22userName%22:%22test-Neil%22%2C%22orgId%22:10%2C%22ocId%22:22%2C%22userType%22:%22OWN%22%2C%22firstLogin%22:false%2C%22ownOrgId%22:null%2C%22clientCode%22:%22%22%2C%22clientType%22:null%2C%22country%22:%22CHN%22%2C%22isGuide%22:null%2C%22acctId%22:null%2C%22userToken%22:null%2C%22deviceId%22:null%2C%22ocCode%22:%**********%22%2C%22secondType%22:null%2C%22vendorCode%22:%2288888%22%2C%22status%22:%22ACTIVE%22%2C%22isDelete%22:false%2C%22email%22:%22%22%2C%22deleteStatus%22:null%2C%22deleteRequestDate%22:null%2C%22wechatId%22:%22%22%2C%22userMfaInfoDTO%22:{%22checkMfa%22:false%2C%22checkSuccess%22:false%2C%22mobile%22:null%2C%22email%22:null%2C%22wechatId%22:null%2C%22totpSecret%22:null}}; ACCESS_TOKEN=Bearer%20b23ee65e-6824-496b-aea6-45fe56137715; _ga_8Y83MQKMZD=GS1.1.**********.1.1.**********.0.0.0; _ga_MTPDGX60Z3=GS1.1.1715853438.1.1.**********.0.0.0; _ga_3F6R7164X1=GS1.1.1715861119.6.0.1715861119.0.0.0; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715; TIMEZONE=+8; TIMEZONE_COUNTRY=CHN; currentOcCode=88880008; currentOcCode=88880008; IMILE_ACCESS_TOKEN=b23ee65e-6824-496b-aea6-45fe56137715")
                .header("front-sec", "{\"orgId\":10,\"entId\":10,\"userCode\":\"210886801\",\"moduleId\":10007,\"client\":\"pc\"}")
                .header("lang", "zh_CN")
                .header("origin", "https://uat-auth.imile.com")
                .header("priority", "u=1, i")
                .header("referer", "https://uat-auth.imile.com/")
                .header("resource-code", "undefined")
                .header("sec-ch-ua", "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"")
                .header("sec-ch-ua-mobile", "?0")
                .header("sec-ch-ua-platform", "\"Windows\"")
                .header("sec-fetch-dest", "empty")
                .header("sec-fetch-mode", "cors")
                .header("sec-fetch-site", "same-origin")
                .header("timezone", "+4")
                .header("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36")
                .body(jsonString)
                .asString();

        String updateResponseBody = updateResponse.getBody();
        Result r2 = JSONObject.parseObject(updateResponseBody, Result.class);
        System.out.println(r2.getStatus());
    }

    @Test
    public void test2() {

    }
}



