package com.imile.permission.jcasbin.api;

import com.imile.permission.jcasbin.domain.dto.DataPermissionDTO;
import com.imile.permission.jcasbin.domain.dto.MenuPermissionDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/10
 */
public class EnforcerApiTest {

    @Test
    public void test() {
        EnforcerApi enforcerApi = getEnforcerApi();
        //RpcResult<List<List<String>>> filteredGroupingPolicy = enforcerApi.getFilteredGroupingPolicy(0, "WC:1740638285026357250");
        RpcResult<List<List<String>>> filteredGroupingPolicy = enforcerApi.getFilteredGroupingPolicy(0, "WC:1740638285026357250",null);
        System.out.println(filteredGroupingPolicy);
    }

    @Test
    public void deleteRolePermission() {
        EnforcerApi enforcerApi = getEnforcerApi();
        enforcerApi.deleteRolePermission(1L);

        List<MenuPermissionDTO> menuPermissionList = new ArrayList<>();
        MenuPermissionDTO menuPermissionDTO = new MenuPermissionDTO();
        menuPermissionDTO.setMenuId(-1129038186413174784L);
        menuPermissionDTO.setMenuIdList(Arrays.asList(1129059803717578753L));
        menuPermissionList.add(menuPermissionDTO);
        enforcerApi.saveRolePermission(1L, menuPermissionList, null);
    }

    @Test
    public void deleteByRole() {
        EnforcerApi enforcerApi = getEnforcerApi();
        enforcerApi.deleteByRole(1L);
    }

    @Test
    public void saveRoleDataPermission() {
        EnforcerApi enforcerApi = getEnforcerApi();
        DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
        dataPermissionDTO.setTypeCode("typeCode1");
        dataPermissionDTO.setTypeName("typeName1");
        dataPermissionDTO.setDataCodeList(Arrays.asList("555"));
        enforcerApi.saveRoleDataPermission(2L, Arrays.asList(dataPermissionDTO));


    }

    public EnforcerApi getEnforcerApi() {
        //EnforcerApi
        // 1.创建服务引用对象实例
        ReferenceConfig<EnforcerApi> referenceConfig = new ReferenceConfig<>();

        // 2.设置应用程序信息
        referenceConfig.setApplication(new ApplicationConfig("EnforcerApiTest"));

        // 3.设置服务注册中心
        referenceConfig.setRegistry(new RegistryConfig("zookeeper://*********:2181"));
        //referenceConfig.setUrl("dubbo://localhost:21881");
        //referenceConfig.setUrl("dubbo://localhost:21882");


        // 4.设置服务接口和超时时间
        referenceConfig.setInterface(EnforcerApi.class);
        referenceConfig.setTimeout(5000);

        // 5.设置自定义负载均衡策略与集群容错策略（以便实现指定ip）
        //referenceConfig.setCluster("customCluster");

        // 6.设置服务分组与版本
        referenceConfig.setVersion("1.0.0");
        return referenceConfig.get();
    }

}
