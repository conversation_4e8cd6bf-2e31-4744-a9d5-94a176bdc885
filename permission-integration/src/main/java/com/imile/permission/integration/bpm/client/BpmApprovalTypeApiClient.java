package com.imile.permission.integration.bpm.client;

import com.imile.bpm.api.BpmApprovalTypeApi;
import com.imile.bpm.mq.dto.ApprovalTypeApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeApiQueryDTO;
import com.imile.permission.util.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.annotation.Reference;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/8/18
 */
public class BpmApprovalTypeApiClient {
    @Reference(version = "1.0.0", check = false, timeout = 6000)
    private BpmApprovalTypeApi bpmApprovalTypeApi;

    public List<ApprovalTypeApiDTO> selectPermissionApprovalTypeList(ApprovalTypeApiQueryDTO query) {
        RpcResult<List<ApprovalTypeApiDTO>> listRpcResult = bpmApprovalTypeApi.selectPermissionApprovalTypeList(query);
        return RpcResultProcessor.process(listRpcResult);
    }
}
