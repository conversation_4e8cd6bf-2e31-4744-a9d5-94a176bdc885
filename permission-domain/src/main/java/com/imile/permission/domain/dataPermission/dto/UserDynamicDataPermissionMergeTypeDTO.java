package com.imile.permission.domain.dataPermission.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/10
 */
@Data
public class UserDynamicDataPermissionMergeTypeDTO {


    /**
     * 数据类型
     */
    private String typeCode;

    /**
     * 归属系统
     */
    private String singleSystem;

    /**
     * 主数据类型 type_code
     */
    private String associatedMainDataTypeCode;

    /**
     * 授权用户Code
     */
    private String userCode;

    /**
     * 数据内容
     */
    private List<String> dataContent;


}
