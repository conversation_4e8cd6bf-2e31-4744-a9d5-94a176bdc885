package com.imile.permission.groovy;

import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
public class GroovyParamJsonProperties {
    public static String getProperty(String properties) throws Exception {
        // 使用 GroovyClassLoader 加载 Groovy 类
        GroovyClassLoader groovyClassLoader = new GroovyClassLoader();
        Class<?> groovyClass = groovyClassLoader.loadClass("com.imile.permission.groovy.ParamJson");
        // 创建 Groovy 对象
        GroovyObject groovyObject = (GroovyObject) groovyClass.newInstance();
        // 使用 Groovy 的 PropertyAccessor 获取属性值
        String message = (String) groovyObject.getProperty(properties);
        return message;
    }

}
