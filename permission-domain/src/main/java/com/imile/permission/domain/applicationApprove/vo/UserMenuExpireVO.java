package com.imile.permission.domain.applicationApprove.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.permission.annotation.OutWithTimeZone;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/12
 */
@Data
public class UserMenuExpireVO {

    private String userCode;

    private String menuId;

    /**
     * 失效日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expirationDate;

    /**
     * 申请日期
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applicationDate;
}
