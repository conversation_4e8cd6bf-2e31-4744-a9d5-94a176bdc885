package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 业务基础数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
public class BusinessBasicDataPermissionConfigUpdateParam {

    private Long id;

    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 动态函数部分
     */
    private List<DynamicDataNestedParam> dataList;


    /**
     * 数据维度部分
     */
    private List<RuleDataNestedParam> ruleList;


    List<MappingFieldInfoParam> fieldInfos;


}
