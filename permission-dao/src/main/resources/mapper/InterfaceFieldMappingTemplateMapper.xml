<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.InterfaceFieldMappingTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.InterfaceFieldMappingTemplateDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="target_field" property="targetField" />
        <result column="target_field_type" property="targetFieldType" />
        <result column="is_display" property="isDisplay" />
        <result column="sort_no" property="sortNo" />
        <result column="mapping_type" property="mappingType" />
        <result column="is_required" property="isRequired" />
        <result column="is_edit_target_field" property="isEditTargetField" />
        <result column="is_edit_display" property="isEditDisplay" />
        <result column="is_edit_search" property="isEditSearch" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, target_field, target_field_type, is_display, sort_no, is_required, mapping_type, is_edit_target_field, is_edit_display, is_edit_search
    </sql>

</mapper>
