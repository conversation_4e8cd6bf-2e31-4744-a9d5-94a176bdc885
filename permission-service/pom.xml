<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.imile</groupId>
        <artifactId>permission</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>permission-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>permission-manage</artifactId>
        </dependency>

        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>permission-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github</groupId>
            <artifactId>easy-log</artifactId>
            <version>${easy-log.version}</version>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>resource-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.8.RELEASE</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>