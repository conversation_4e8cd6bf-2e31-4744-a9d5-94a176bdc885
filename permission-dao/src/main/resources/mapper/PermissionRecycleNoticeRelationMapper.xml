<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.PermissionRecycleNoticeRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.PermissionRecycleNoticeRelationDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="notice_id" property="noticeId" />
        <result column="user_code" property="userCode" />
        <result column="system_code" property="systemCode" />
        <result column="permission_type" property="permissionType" />
        <result column="permission_id" property="permissionId" />
        <result column="expirationDate" property="expirationDate" />
        <result column="sendDate" property="sendDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, notice_id, user_code, system_code, permission_type, permission_id, expirationDate, sendDate
    </sql>

</mapper>
