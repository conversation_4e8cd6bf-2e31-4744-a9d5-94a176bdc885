package com.imile.permission.manage;

import cn.hutool.core.net.NetUtil;
import com.imile.permission.ApplicationTest;
import com.imile.permission.domain.dataPermission.param.BusinessBasicDataParam;
import com.imile.permission.domain.dataPermission.param.BusinessBasicDataPermissionConfigAddParam;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataListVO;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataPermissionConfigVO;
import com.imile.permission.domain.dataPermission.vo.BusinessBasicDataTreeVO;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */

public class DataPermissionManageTest extends ApplicationTest {

    @BeforeAll
    public static void before() {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        System.setProperty("apollo.meta", "http://*********:8081");
    }

    @Autowired
    DataPermissionManage dataPermissionManage;


    @Test
    public void addBusinessBasicDataConfig() {
        BusinessBasicDataPermissionConfigAddParam businessBasicDataPermissionConfigAddParam = new BusinessBasicDataPermissionConfigAddParam();
        businessBasicDataPermissionConfigAddParam.setDataStructures(0);
        businessBasicDataPermissionConfigAddParam.setDataUrl("https://test-auth.52imile.cn/ipv6.1433.eu.org");
        businessBasicDataPermissionConfigAddParam.setTypeCode("SupplierEnum");
        businessBasicDataPermissionConfigAddParam.setTypeName("供应商枚举");
        dataPermissionManage.addBusinessBasicDataConfig(
                businessBasicDataPermissionConfigAddParam
        );
    }

    @Test
    public void deleteBusinessBasicDataConfig() {
        dataPermissionManage.deleteBusinessBasicDataConfig(1749414122631507970L);
    }

    @Test
    public void listBusinessBasicDataConfig() {
        List<BusinessBasicDataPermissionConfigVO> businessBasicDataPermissionConfigVOS = dataPermissionManage.listBusinessBasicDataConfig(null);
        System.out.println(businessBasicDataPermissionConfigVOS);
    }

    @Test
    public void treeBusinessBasicData() {
        BusinessBasicDataParam businessBasicDataParam = new BusinessBasicDataParam();
        businessBasicDataParam.setTypeCode("HrmsEntDeptNewTree");
        businessBasicDataParam.setDataUrl("https://dev-ehr.52imile.cn/hrms/ent/dept/new/tree");
        UcenterUtils.setUToken("27479983-3915-40d1-9a73-f1997f671316");
        List<BusinessBasicDataTreeVO> businessBasicDataListVO = dataPermissionManage.treeBusinessBasicData(businessBasicDataParam);
        System.out.println(businessBasicDataListVO);
    }

    @Test
    public void listBusinessBasicData() {
        UcenterUtils.setUToken("93608692-6823-4093-88c3-c1bb3a5d062b");
        BusinessBasicDataParam businessBasicDataParam = new BusinessBasicDataParam();
        businessBasicDataParam.setTypeCode("SupplierEnum");
        businessBasicDataParam.setTypeName("供应商枚举");
        businessBasicDataParam.setDataUrl("https://dev-oa.52imile.cn/purchase/supplier/out/selectAllSupplierByPass");
        BusinessBasicDataListVO businessBasicDataListVO = dataPermissionManage.listBusinessBasicData(businessBasicDataParam);
        System.out.println(businessBasicDataListVO);
    }


}
