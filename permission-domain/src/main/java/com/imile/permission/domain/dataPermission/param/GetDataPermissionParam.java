package com.imile.permission.domain.dataPermission.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/13
 */
@Data
public class GetDataPermissionParam {

    /**
     * 系统code
     */
    private List<String> systemCodeList;

    /**
     * 查询场景的类型
     * 1-角色；2-员工
     */
    private Integer sceneType = 1;

    /**
     * 是否员工授权
     */
    private Integer isAuthUser;
}
