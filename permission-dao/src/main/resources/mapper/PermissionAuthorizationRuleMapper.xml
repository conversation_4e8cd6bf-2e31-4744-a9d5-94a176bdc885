<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.permission.mapper.PermissionAuthorizationRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.permission.domain.entity.PermissionAuthorizationRuleDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="authorization_type" property="authorizationType" />
        <result column="authorization_user_code" property="authorizationUserCode" />
        <result column="authorization_role_id" property="authorizationRoleId" />
        <result column="relation_type" property="relationType" />
        <result column="relation_type_code" property="relationTypeCode" />
        <result column="authorization_rule_json" property="authorizationRuleJson" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, authorization_type, authorization_user_code, authorization_role_id, relation_type, relation_type_code, authorization_rule_json
    </sql>

</mapper>
