package com.imile.permission.domain.dataPermission.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imile.permission.domain.dataPermission.vo.DynamicDataCodeVO;
import com.imile.permission.domain.dataPermission.vo.RuleDataVO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
public class SingleDimensionVO {
    /**
     * 数据结构类型（0:列表 1:树）
     */
    private Integer dataStructures;
    /**
     * 数据接口 url
     */
    private String dataUrl;
    /**
     * 请求类型
     */
    private String requestType;
    /**
     * 数据类型
     */
    private String typeCode;
    /**
     * 数据类型名称
     */
    private String typeName;
    /**
     * 数据类型英文名称
     */
    private String typeNameEn;

    /**
     * 数据类型显示名称
     */
    private String showTypeName;
    /**
     * 使用场景解释
     */
    private String useCaseDescription;

    /**
     * 是否支持搜索 默认否
     */
    private Integer isSupportSearch = 0;

    /**
     * 函数数据
     */
    private List<DynamicDataCodeVO> dynamicDataCodeList;

    /**
     * 数据维度
     */
    private List<RuleDataVO> ruleDataList;

    @JsonIgnore
    private Date createDate;
}
