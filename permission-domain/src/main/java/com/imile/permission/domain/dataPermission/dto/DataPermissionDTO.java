package com.imile.permission.domain.dataPermission.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class DataPermissionDTO implements Serializable {
    /**
     * 数据字典类型
     */
    private String typeCode;

    /**
     * 数据字典名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String typeName;

    /**
     * 字典数据
     */
    private List<String> dataCodeList;

    /**
     * 全选
     */
    private Integer selectAll;

    /**
     * 扩展标签
     */
    private List<DataCodeExtensionTagDTO> dataTagList;


    @JsonIgnore
    private List<String> notSelectDataCodeList;
}
