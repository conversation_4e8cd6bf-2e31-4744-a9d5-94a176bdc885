package com.imile.permission.domain.dataPermission.vo;

import com.imile.permission.domain.dataPermission.dto.DataPermissionDTO;
import com.imile.permission.domain.dataPermission.dto.DataPermissionRuleDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SystemDataPermissionRuleVO {

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 数据权限
     */
    private List<DataPermissionRuleDTO> dataPermissionDTOList = new ArrayList<>();

}
