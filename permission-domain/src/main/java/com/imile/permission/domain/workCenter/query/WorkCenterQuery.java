package com.imile.permission.domain.workCenter.query;

import com.imile.permission.domain.common.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterQuery extends ResourceQuery {

    /**
     * 主键ID
     */
    private Long id;


    /**
     * 主键ID 集合
     */
    private List<Long> idList;

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 授权类型（1:公共访问 2:授权角色）
     */
    private Integer authorizationType;

    /**
     * 系统code
     */
    private List<String> systemCodeList;

    /**
     * 是否禁用（0:启用 1:禁用）
     */
    private Integer isDisable;

    /**
     * 是否过滤节点为空的数据
     * true-是 false-否
     */
    private boolean filterNullNode = false;
}
