package com.imile.permission.domain.workCenter.param;

import com.imile.common.page.Pagination;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
public class WorkCenterPageQueryParam extends Pagination {

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 授权类型（1:公共访问 2:授权角色）
     */
    @Deprecated
    private Integer authorizationType;

    /**
     * 角色
     */
    @Deprecated
    private Long roleId;

    /**
     * 角色集合
     */
    @Deprecated
    private List<Long> roleIdList;

    /**
     * 工作中心ID集合
     */
    private List<Long> selectWorkCenterIdList;

    /**
     * 系统code
     */
    private String systemCode;

}
